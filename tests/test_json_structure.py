#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON文件结构和音频文件可用性
"""

import json
import os
from pathlib import Path


def test_json_structure(json_file="deer-flow/real_tool_call_result.json"):
    """测试JSON文件结构"""
    print("=" * 60)
    print("测试JSON文件结构和音频文件可用性")
    print("=" * 60)
    
    # 检查JSON文件是否存在
    if not os.path.exists(json_file):
        print(f"❌ JSON文件不存在: {json_file}")
        return False
    
    print(f"✅ JSON文件存在: {json_file}")
    
    try:
        # 读取JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("✅ JSON文件格式正确")
        
        # 检查必要的字段
        required_fields = ['media_info', 'speaker_info', 'subtitles', 'audio_segments']
        for field in required_fields:
            if field in data:
                print(f"✅ 包含字段: {field}")
            else:
                print(f"❌ 缺少字段: {field}")
                return False
        
        # 分析说话人信息
        speaker_info = data['speaker_info']
        print(f"\n📊 说话人信息:")
        for speaker_id, speaker_name in speaker_info.items():
            print(f"  说话人 {speaker_id}: {speaker_name}")
        
        # 分析字幕信息
        subtitles = data['subtitles']
        print(f"\n📊 字幕信息:")
        print(f"  总字幕条数: {len(subtitles)}")
        
        # 按说话人统计
        speaker_counts = {}
        for subtitle in subtitles:
            speaker_id = subtitle['speaker_id']
            speaker_counts[speaker_id] = speaker_counts.get(speaker_id, 0) + 1
        
        for speaker_id, count in speaker_counts.items():
            speaker_name = speaker_info.get(speaker_id, "未知")
            print(f"  说话人 {speaker_id} ({speaker_name}): {count} 条字幕")
        
        # 检查音频片段
        audio_segments = data.get('audio_segments', {})
        print(f"\n📊 音频片段信息:")
        
        total_audio_files = 0
        existing_files = 0
        missing_files = []
        
        for speaker_name, speaker_data in audio_segments.items():
            segments = speaker_data.get('segments', [])
            print(f"  {speaker_name}: {len(segments)} 个音频片段")
            
            for i, segment in enumerate(segments[:5]):  # 只检查前5个文件
                file_path = segment['file_path']
                total_audio_files += 1
                
                if os.path.exists(file_path):
                    existing_files += 1
                    print(f"    ✅ 文件 {i+1}: {Path(file_path).name}")
                else:
                    missing_files.append(file_path)
                    print(f"    ❌ 文件 {i+1}: {Path(file_path).name} (不存在)")
            
            if len(segments) > 5:
                print(f"    ... 还有 {len(segments) - 5} 个文件")
                # 检查剩余文件
                for segment in segments[5:]:
                    file_path = segment['file_path']
                    total_audio_files += 1
                    if os.path.exists(file_path):
                        existing_files += 1
                    else:
                        missing_files.append(file_path)
        
        print(f"\n📊 音频文件统计:")
        print(f"  总文件数: {total_audio_files}")
        print(f"  存在文件: {existing_files}")
        print(f"  缺失文件: {len(missing_files)}")
        
        if missing_files:
            print(f"\n❌ 缺失的音频文件 (前10个):")
            for file_path in missing_files[:10]:
                print(f"    {file_path}")
            if len(missing_files) > 10:
                print(f"    ... 还有 {len(missing_files) - 10} 个缺失文件")
        
        # 总结
        print(f"\n📋 总结:")
        if existing_files == total_audio_files:
            print("✅ 所有音频文件都存在，可以开始分割")
            return True
        elif existing_files > 0:
            print(f"⚠️  部分音频文件存在 ({existing_files}/{total_audio_files})，可以尝试分割")
            return True
        else:
            print("❌ 没有找到任何音频文件，无法进行分割")
            return False
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False


def main():
    """主函数"""
    success = test_json_structure()
    
    if success:
        print("\n🚀 建议运行命令:")
        print("cd deer-flow")
        print("python run_split.py")
    else:
        print("\n💡 请检查JSON文件和音频文件路径")


if __name__ == "__main__":
    main()
