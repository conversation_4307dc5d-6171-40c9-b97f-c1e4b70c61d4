#!/usr/bin/env python3
"""
测试修复的问题：speaker_id和file_path
"""

import logging
import sys
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_fixed_issues():
    """测试修复的问题"""
    logger.info("🔧 测试修复的问题...")
    
    # 测试视频URL
    video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Km5kzHEim_GAOQING.mp4"
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入配置和工具
        from config.configuration import Configuration
        from tools.audio.video_subtitle_extraction import get_video_subtitle_extraction_tool
        
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 创建工具
        tool = get_video_subtitle_extraction_tool(config)
        
        if tool is None:
            logger.error("❌ 工具创建失败")
            return False
        
        logger.info("✅ 视频字幕提取工具创建成功")
        
        # 准备测试参数 - 启用COS存储
        test_params = {
            "media_url": video_url,
            "language": "zh-CN",
            "auto_speaker_identification": True,
            "output_format": "json",
            "precision_level": "sentence",
            "extract_audio_segments": True,
            "save_to_cos": True,  # 启用COS存储
            "cos_bucket_prefix": "test-fixed-issues"
        }
        
        logger.info("🚀 开始测试修复的问题...")
        logger.info("📋 测试参数:")
        for key, value in test_params.items():
            logger.info(f"   {key}: {value}")
        
        # 调用工具
        result = tool.invoke(test_params)
        
        logger.info("✅ 视频字幕提取完成")
        
        # 解析结果
        if isinstance(result, str):
            result_data = json.loads(result)
        else:
            result_data = result
        
        # 检查问题1：speaker_id应该是数字
        logger.info("\n" + "="*60)
        logger.info("🔍 检查问题1：speaker_id应该是数字")
        logger.info("="*60)
        
        if "subtitles" in result_data:
            subtitles = result_data["subtitles"]
            speaker_ids = set()
            
            for subtitle in subtitles[:5]:  # 检查前5个
                speaker_id = subtitle.get("speaker_id", "")
                speaker_name = subtitle.get("speaker_name", "")
                text = subtitle.get("text", "")
                
                speaker_ids.add(speaker_id)
                logger.info(f"   speaker_id: '{speaker_id}', speaker_name: '{speaker_name}', text: '{text[:30]}...'")
            
            # 验证speaker_id是否为数字
            all_numeric = all(str(sid).isdigit() for sid in speaker_ids)
            if all_numeric:
                logger.info("✅ 问题1已修复：speaker_id现在是数字格式")
            else:
                logger.error("❌ 问题1未修复：speaker_id仍然不是数字格式")
                logger.info(f"   发现的speaker_ids: {speaker_ids}")
        
        # 检查问题2：file_path应该是COS URL
        logger.info("\n" + "="*60)
        logger.info("🔍 检查问题2：file_path应该是COS URL")
        logger.info("="*60)
        
        if "audio_segments" in result_data:
            audio_segments = result_data["audio_segments"]
            
            for speaker_name, speaker_data in audio_segments.items():
                if isinstance(speaker_data, dict) and "segments" in speaker_data:
                    segments = speaker_data["segments"]
                    logger.info(f"   说话人: {speaker_name}")
                    
                    for i, segment in enumerate(segments[:3]):  # 检查前3个
                        file_path = segment.get("file_path", "")
                        text = segment.get("text", "")
                        
                        logger.info(f"      片段{i+1}: {text[:20]}...")
                        logger.info(f"      file_path: {file_path}")
                        
                        # 验证file_path是否为COS URL
                        is_cos_url = file_path.startswith("https://") and "cos.ap-guangzhou.myqcloud.com" in file_path
                        if is_cos_url:
                            logger.info("      ✅ 这是COS URL")
                        else:
                            logger.error("      ❌ 这不是COS URL")
                    
                    if len(segments) > 3:
                        logger.info(f"      ... 还有 {len(segments) - 3} 个片段")
                    
                    break  # 只检查第一个说话人
        
        # 检查COS URLs
        logger.info("\n" + "="*60)
        logger.info("🔍 检查COS存储URLs")
        logger.info("="*60)
        
        if "cos_urls" in result_data:
            cos_urls = result_data["cos_urls"]
            
            if "json_result" in cos_urls:
                json_url = cos_urls["json_result"]
                logger.info(f"📄 JSON结果URL: {json_url}")
                
                # 验证URL格式
                if json_url.startswith("https://") and "cos.ap-guangzhou.myqcloud.com" in json_url:
                    logger.info("✅ JSON URL格式正确")
                else:
                    logger.error("❌ JSON URL格式错误")
            
            if "audio_segments" in cos_urls:
                audio_cos = cos_urls["audio_segments"]
                logger.info(f"🎤 音频片段COS URLs:")
                
                for speaker_name, segments in audio_cos.items():
                    logger.info(f"   {speaker_name}: {len(segments)} 个文件")
                    
                    for i, segment in enumerate(segments[:2]):  # 显示前2个
                        cos_url = segment.get("cos_url", "")
                        text = segment.get("text", "")
                        
                        logger.info(f"      {i+1}. {text[:20]}...")
                        logger.info(f"         URL: {cos_url}")
                        
                        # 验证URL格式
                        if cos_url.startswith("https://") and "cos.ap-guangzhou.myqcloud.com" in cos_url:
                            logger.info("         ✅ COS URL格式正确")
                        else:
                            logger.error("         ❌ COS URL格式错误")
                    
                    if len(segments) > 2:
                        logger.info(f"      ... 还有 {len(segments) - 2} 个文件")
                    
                    break  # 只检查第一个说话人
            
            if "summary" in cos_urls:
                summary_url = cos_urls["summary"]
                logger.info(f"📋 汇总信息URL: {summary_url}")
                
                # 验证URL格式
                if summary_url.startswith("https://") and "cos.ap-guangzhou.myqcloud.com" in summary_url:
                    logger.info("✅ 汇总URL格式正确")
                else:
                    logger.error("❌ 汇总URL格式错误")
        
        # 保存测试结果
        output_file = "fixed_issues_test_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        logger.info(f"\n💾 测试结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🎯 开始测试修复的问题...")
    logger.info("目标: 验证speaker_id和file_path的修复")
    
    success = test_fixed_issues()
    
    logger.info(f"\n{'='*60}")
    logger.info("🎯 修复问题测试总结")
    logger.info(f"{'='*60}")
    
    if success:
        logger.info("✅ 测试成功完成")
        logger.info("")
        logger.info("🔧 **修复的问题:**")
        logger.info("   1. speaker_id现在使用数字格式（1, 2, 3...）")
        logger.info("   2. file_path现在使用COS URL而不是本地路径")
        logger.info("")
        logger.info("📁 **生成的文件:**")
        logger.info("   • fixed_issues_test_result.json - 修复后的完整结果")
        logger.info("")
        logger.info("☁️ **COS存储:**")
        logger.info("   • JSON结果文件已上传到COS")
        logger.info("   • 所有音频片段已上传到COS")
        logger.info("   • 汇总信息已上传到COS")
        logger.info("")
        logger.info("🎉 **修复验证成功！**")
        logger.info("现在返回的JSON中包含正确的speaker_id和COS URL！")
    else:
        logger.error("❌ 测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
