#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能增强女主角音频 - 选择最佳片段并生成高质量音频
"""

from smart_audio_splitter import smart_split_speaker_audio
import os

def main():
    """智能增强女主角音频"""
    print("=" * 60)
    print("智能音频处理 - 女主角（患者家属）高质量音频生成")
    print("=" * 60)
    
    # 检查JSON文件
    json_file = "real_tool_call_result.json"
    if not os.path.exists(json_file):
        print(f"错误: 找不到文件 {json_file}")
        return
    
    print("🎯 超严格选择策略:")
    print("  ✅ 只选择4-12秒的高质量片段")
    print("  ✅ 严格过滤噪音和不连贯句子")
    print("  ✅ 排除可能包含其他人声音的片段")
    print("  ✅ 只要10秒以上的精华内容")
    print("  ✅ 最低分数门槛200分以上")
    print("-" * 60)
    
    try:
        # 智能处理说话人3
        success = smart_split_speaker_audio(
            speaker_id="3",
            json_file=json_file,
            output_dir="ultra_clean_audio",
            target_duration=12
        )
        
        if success:
            print("\n" + "=" * 60)
            print("✅ 女主角超净化音频生成完成！")
            print("📁 文件保存在: ultra_clean_audio/")
            print("🎵 这次只选择了最纯净的音频片段")
            print("💡 严格过滤，只要10秒以上精华内容")
            print("=" * 60)
            
            # 显示文件信息
            smart_dir = "ultra_clean_audio"
            if os.path.exists(smart_dir):
                files = [f for f in os.listdir(smart_dir) if f.endswith('.wav')]
                if files:
                    print(f"\n📂 生成的文件:")
                    for file in files:
                        file_path = os.path.join(smart_dir, file)
                        size = os.path.getsize(file_path) / 1024 / 1024  # MB
                        print(f"  📄 {file} ({size:.1f}MB)")
        else:
            print("\n❌ 智能音频生成失败")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
