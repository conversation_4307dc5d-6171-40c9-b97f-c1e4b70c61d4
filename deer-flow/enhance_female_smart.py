#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能增强女主角音频 - 选择最佳片段并生成高质量音频
"""

from smart_audio_splitter import smart_split_speaker_audio
import os

def main():
    """智能增强女主角音频"""
    print("=" * 60)
    print("智能音频处理 - 女主角（患者家属）高质量音频生成")
    print("=" * 60)
    
    # 检查JSON文件
    json_file = "real_tool_call_result.json"
    if not os.path.exists(json_file):
        print(f"错误: 找不到文件 {json_file}")
        return
    
    print("🎯 智能选择策略:")
    print("  ✅ 优先选择3-15秒的中短句子")
    print("  ✅ 选择完整、流畅的语句")
    print("  ✅ 避免过多停顿词和重复")
    print("  ✅ 按内容质量评分排序")
    print("-" * 60)
    
    try:
        # 智能处理说话人3
        success = smart_split_speaker_audio(
            speaker_id="3",
            json_file=json_file,
            output_dir="smart_audio",
            target_duration=60
        )
        
        if success:
            print("\n" + "=" * 60)
            print("✅ 女主角智能音频生成完成！")
            print("📁 文件保存在: smart_audio/")
            print("🎵 这次选择的都是高质量的音频片段")
            print("💡 音质应该比之前的版本更清晰流畅")
            print("=" * 60)
            
            # 显示文件信息
            smart_dir = "smart_audio"
            if os.path.exists(smart_dir):
                files = [f for f in os.listdir(smart_dir) if f.endswith('.wav')]
                if files:
                    print(f"\n📂 生成的文件:")
                    for file in files:
                        file_path = os.path.join(smart_dir, file)
                        size = os.path.getsize(file_path) / 1024 / 1024  # MB
                        print(f"  📄 {file} ({size:.1f}MB)")
        else:
            print("\n❌ 智能音频生成失败")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
