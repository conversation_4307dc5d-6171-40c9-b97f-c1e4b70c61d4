
import os
import json
import time
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from aliyunsdkcore.auth.credentials import AccessKeyCredential

# 配置
access_key_id = "LTAI5tBTpMAPNJ3Z6jpZyTSo"
access_key_secret = "******************************"
app_key = "wbp1hepOKEWDiQEC"

# 创建客户端
credentials = AccessKeyCredential(access_key_id, access_key_secret)
client = AcsClient(region_id='cn-beijing', credential=credentials)

# 请求体
request_body = {
    "AppKey": "wbp1hepOKEWDiQEC",
    "Input": {
        "SourceLanguage": "cn",
        "TaskKey": "test_speaker_diarization_manual",
        "FileUrl": "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
    },
    "Parameters": {
        "Transcription": {
            "DiarizationEnabled": True,
            "Diarization": {
                "SpeakerCount": 0
            }
        },
        "AutoChaptersEnabled": False,
        "MeetingAssistanceEnabled": False,
        "SummarizationEnabled": False,
        "TranslationEnabled": False,
        "TextPolishEnabled": False
    }
}

# 提交任务
request = CommonRequest()
request.set_accept_format('json')
request.set_domain('tingwu.cn-beijing.aliyuncs.com')
request.set_version('2023-09-30')
request.set_protocol_type('https')
request.set_method('PUT')
request.set_uri_pattern('/openapi/tingwu/v2/tasks')
request.add_header('Content-Type', 'application/json')
request.add_query_param('type', 'offline')
request.set_content(json.dumps(request_body).encode('utf-8'))

response = client.do_action_with_exception(request)
result = json.loads(response)
print("提交结果:", json.dumps(result, indent=2, ensure_ascii=False))

if result.get('Code') == '0':
    task_id = result['Data']['TaskId']
    print(f"任务ID: {task_id}")
    
    # 轮询查询结果
    while True:
        query_request = CommonRequest()
        query_request.set_accept_format('json')
        query_request.set_domain('tingwu.cn-beijing.aliyuncs.com')
        query_request.set_version('2023-09-30')
        query_request.set_protocol_type('https')
        query_request.set_method('GET')
        query_request.set_uri_pattern(f'/openapi/tingwu/v2/tasks/{task_id}')
        
        query_response = client.do_action_with_exception(query_request)
        query_result = json.loads(query_response)
        
        status = query_result['Data']['TaskStatus']
        print(f"任务状态: {status}")
        
        if status == 'COMPLETED':
            print("任务完成!")
            print(json.dumps(query_result, indent=2, ensure_ascii=False))
            break
        elif status == 'FAILED':
            print("任务失败!")
            break
        else:
            time.sleep(30)  # 等待30秒后再查询
