#!/usr/bin/env python3
"""
测试COS存储功能
"""

import logging
import sys
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cos_config():
    """测试COS配置"""
    logger.info("🔧 测试COS配置...")
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入COS配置函数
        from tools.audio.video_subtitle_extraction import _get_cos_config
        
        config = _get_cos_config()
        
        logger.info("📋 COS配置检查:")
        logger.info(f"   可用性: {config.get('available', False)}")
        
        if config["available"]:
            logger.info(f"   区域: {config.get('region', 'unknown')}")
            logger.info(f"   存储桶: {config.get('bucket', 'unknown')}")
            logger.info(f"   Secret ID: {config.get('secret_id', 'unknown')[:10]}...")
            logger.info("   ✅ COS配置完整")
            return True
        else:
            logger.warning(f"   ⚠️ COS配置缺失: {config.get('error', 'unknown')}")
            logger.info("💡 请设置以下环境变量:")
            logger.info("   export TENCENT_COS_SECRET_ID='your_secret_id'")
            logger.info("   export TENCENT_COS_SECRET_KEY='your_secret_key'")
            logger.info("   export TENCENT_COS_BUCKET='your_bucket'")
            logger.info("   export TENCENT_COS_REGION='ap-guangzhou'")
            return False
        
    except Exception as e:
        logger.error(f"❌ COS配置测试失败: {e}")
        return False

def test_enhanced_input_model():
    """测试增强的输入模型"""
    logger.info("📝 测试增强的输入模型...")
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入输入模型
        from tools.audio.video_subtitle_extraction import VideoSubtitleExtractionInput
        
        # 测试基础功能
        basic_input = VideoSubtitleExtractionInput(
            media_url="https://example.com/test.mp4",
            language="zh-CN"
        )
        
        logger.info("✅ 基础输入模型创建成功")
        logger.info(f"   save_to_cos: {basic_input.save_to_cos}")
        logger.info(f"   cos_bucket_prefix: {basic_input.cos_bucket_prefix}")
        
        # 测试COS功能
        cos_input = VideoSubtitleExtractionInput(
            media_url="https://example.com/zhaobenshang.mp4",
            language="zh-CN",
            auto_speaker_identification=True,
            save_to_cos=True,
            cos_bucket_prefix="ai-recreation/zhaobenshang",
            extract_audio_segments=True
        )
        
        logger.info("✅ COS增强输入模型创建成功")
        logger.info(f"   参数: {cos_input.dict()}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 输入模型测试失败: {e}")
        return False

def test_cos_integration():
    """测试COS集成"""
    logger.info("🔗 测试COS集成...")
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入配置和工具
        from config.configuration import Configuration
        from tools.audio.video_subtitle_extraction import get_video_subtitle_extraction_tool
        
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 创建工具
        tool = get_video_subtitle_extraction_tool(config)
        
        if tool is None:
            logger.warning("⚠️ 工具创建失败（可能缺少Volcengine配置）")
            return False
        
        logger.info("✅ 增强工具创建成功")
        
        # 检查工具描述是否包含COS功能
        description = tool.description
        if "COS cloud storage" in description:
            logger.info("✅ 工具描述包含COS存储功能")
        else:
            logger.warning("⚠️ 工具描述可能未更新COS功能")
        
        # 测试args_schema
        if hasattr(tool, 'args_schema'):
            schema = tool.args_schema
            
            # 创建包含COS功能的测试实例
            test_instance = schema(
                media_url="https://example.com/test.mp4",
                save_to_cos=True,
                cos_bucket_prefix="test-ai-recreation"
            )
            
            logger.info("✅ COS功能参数验证成功")
            logger.info(f"   save_to_cos: {test_instance.save_to_cos}")
            logger.info(f"   cos_bucket_prefix: {test_instance.cos_bucket_prefix}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ COS集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cos_workflow_simulation():
    """模拟COS工作流程"""
    logger.info("🎭 模拟COS工作流程...")
    
    try:
        # 模拟处理结果
        mock_json_result = json.dumps({
            "media_info": {
                "duration": 476.31,
                "format": "mp4",
                "is_video": True
            },
            "speaker_info": {
                "1": "赵本山",
                "2": "宋丹丹"
            },
            "subtitles": [
                {
                    "speaker_id": "1",
                    "speaker_name": "赵本山",
                    "text": "这个小品啊，就是要让大家开心",
                    "start_time": 1200,
                    "end_time": 3500,
                    "duration": 2300
                }
            ],
            "statistics": {
                "total_duration": 397200,
                "total_words": 1602
            }
        }, ensure_ascii=False, indent=2)
        
        mock_audio_segments = {
            "赵本山": [
                {
                    "file_path": "/tmp/mock_audio_1.wav",
                    "start_time": 1.2,
                    "end_time": 3.5,
                    "text": "这个小品啊，就是要让大家开心"
                }
            ],
            "宋丹丹": [
                {
                    "file_path": "/tmp/mock_audio_2.wav", 
                    "start_time": 4.0,
                    "end_time": 6.5,
                    "text": "是啊，我们要演得自然一点"
                }
            ]
        }
        
        logger.info("📊 模拟数据准备完成:")
        logger.info(f"   JSON大小: {len(mock_json_result)} 字符")
        logger.info(f"   音频片段: {sum(len(segments) for segments in mock_audio_segments.values())} 个")
        logger.info(f"   说话人: {list(mock_audio_segments.keys())}")
        
        # 模拟COS存储结构
        expected_cos_structure = {
            "json_result": "https://bucket.cos.ap-guangzhou.myqcloud.com/ai-recreation/20250805_123456_bf6a8db2/subtitle_result.json",
            "audio_segments": {
                "赵本山": [
                    {
                        "segment_index": 1,
                        "text": "这个小品啊，就是要让大家开心",
                        "cos_url": "https://bucket.cos.ap-guangzhou.myqcloud.com/ai-recreation/20250805_123456_bf6a8db2/audio_segments/赵本山/赵本山_001.wav"
                    }
                ],
                "宋丹丹": [
                    {
                        "segment_index": 1,
                        "text": "是啊，我们要演得自然一点",
                        "cos_url": "https://bucket.cos.ap-guangzhou.myqcloud.com/ai-recreation/20250805_123456_bf6a8db2/audio_segments/宋丹丹/宋丹丹_001.wav"
                    }
                ]
            },
            "summary": "https://bucket.cos.ap-guangzhou.myqcloud.com/ai-recreation/20250805_123456_bf6a8db2/summary.json"
        }
        
        logger.info("🗂️ 预期COS存储结构:")
        logger.info(f"   JSON结果URL: {expected_cos_structure['json_result']}")
        logger.info(f"   音频片段URL数量: {sum(len(segments) for segments in expected_cos_structure['audio_segments'].values())}")
        logger.info(f"   汇总信息URL: {expected_cos_structure['summary']}")
        
        # 模拟AI二创工作流
        logger.info("🎬 AI二创工作流模拟:")
        logger.info("   1. 视频字幕提取 → 生成JSON和音频片段")
        logger.info("   2. 保存到COS → 获得持久化URL")
        logger.info("   3. 声音克隆训练 → 使用COS音频片段URL")
        logger.info("   4. 文本生成 → 基于COS JSON结构")
        logger.info("   5. 多人TTS合成 → 生成新音频")
        logger.info("   6. 视频制作 → 结合新音频和视觉内容")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ COS工作流程模拟失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始COS存储功能测试...")
    logger.info("目标: 验证视频字幕提取工具的COS存储集成")
    
    tests = [
        ("COS配置", test_cos_config),
        ("增强输入模型", test_enhanced_input_model),
        ("COS集成", test_cos_integration),
        ("COS工作流程模拟", test_cos_workflow_simulation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 COS存储功能测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count >= 3:  # 至少3个测试通过
        logger.info("\n🎉 COS存储功能集成成功！")
        logger.info("")
        logger.info("🚀 **新增功能:**")
        logger.info("   ✅ COS云存储集成")
        logger.info("   ✅ 持久化JSON结果")
        logger.info("   ✅ 音频片段云存储")
        logger.info("   ✅ 自动文件组织")
        logger.info("   ✅ 可访问URL生成")
        logger.info("")
        logger.info("🎭 **AI二创优势:**")
        logger.info("   • 持久化存储 - 避免临时文件丢失")
        logger.info("   • 便于分享 - 生成可访问的URL")
        logger.info("   • 批量处理 - 支持大规模AI二创项目")
        logger.info("   • 成本优化 - 云存储比本地存储更经济")
        logger.info("   • 集成友好 - 便于与其他AI工具集成")
        logger.info("")
        logger.info("📁 **COS存储结构:**")
        logger.info("   ai-recreation/")
        logger.info("   ├── 20250805_123456_taskid/")
        logger.info("   │   ├── subtitle_result.json")
        logger.info("   │   ├── summary.json")
        logger.info("   │   └── audio_segments/")
        logger.info("   │       ├── 赵本山/")
        logger.info("   │       │   ├── 赵本山_001.wav")
        logger.info("   │       │   └── 赵本山_002.wav")
        logger.info("   │       └── 宋丹丹/")
        logger.info("   │           ├── 宋丹丹_001.wav")
        logger.info("   │           └── 宋丹丹_002.wav")
        logger.info("")
        logger.info("🎤 **COS存储功能已准备就绪！**")
        logger.info("现在可以将AI二创素材持久化存储到云端！")
    else:
        logger.warning("⚠️ 部分测试失败，COS功能可能需要进一步配置")

if __name__ == "__main__":
    main()
