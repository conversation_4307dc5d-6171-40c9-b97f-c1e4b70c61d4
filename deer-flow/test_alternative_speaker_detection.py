#!/usr/bin/env python3
"""
测试替代的说话人识别方案
"""

import logging
import sys
import os
import json
import numpy as np
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_speaker_detection_by_pause(subtitles: List[Dict], pause_threshold: float = 2.0) -> List[Dict]:
    """
    基于停顿时间的简单说话人检测
    假设：不同说话人之间通常有较长的停顿
    """
    logger.info(f"🎤 基于停顿时间进行说话人检测 (停顿阈值: {pause_threshold}秒)")
    
    if not subtitles:
        return subtitles
    
    current_speaker_id = 1
    previous_end_time = 0
    
    for i, subtitle in enumerate(subtitles):
        start_time = subtitle.get("start_time", 0) / 1000.0  # 转换为秒
        
        # 如果这不是第一句话，检查与前一句的间隔
        if i > 0:
            pause_duration = start_time - previous_end_time
            
            # 如果停顿时间超过阈值，认为是新的说话人
            if pause_duration > pause_threshold:
                current_speaker_id += 1
                logger.info(f"   检测到说话人切换: 停顿{pause_duration:.1f}秒 -> 说话人{current_speaker_id}")
        
        # 更新说话人信息
        subtitle["speaker_id"] = str(current_speaker_id)
        subtitle["speaker_name"] = f"说话人{current_speaker_id}"
        
        previous_end_time = subtitle.get("end_time", 0) / 1000.0
    
    # 统计说话人
    speaker_count = len(set(s.get("speaker_id") for s in subtitles))
    logger.info(f"✅ 检测到 {speaker_count} 个说话人")
    
    return subtitles

def simple_speaker_detection_by_duration(subtitles: List[Dict], min_duration: float = 0.5, max_duration: float = 10.0) -> List[Dict]:
    """
    基于语句时长的简单说话人检测
    假设：不同说话人的语句时长模式不同
    """
    logger.info(f"🎤 基于语句时长进行说话人检测")
    
    if not subtitles:
        return subtitles
    
    # 计算每句话的时长
    durations = []
    for subtitle in subtitles:
        start_time = subtitle.get("start_time", 0) / 1000.0
        end_time = subtitle.get("end_time", 0) / 1000.0
        duration = end_time - start_time
        durations.append(duration)
    
    # 简单的聚类：短句、中等句、长句
    short_threshold = np.percentile(durations, 33)
    long_threshold = np.percentile(durations, 67)
    
    logger.info(f"   时长阈值: 短句<{short_threshold:.1f}s, 长句>{long_threshold:.1f}s")
    
    for i, subtitle in enumerate(subtitles):
        duration = durations[i]
        
        if duration < short_threshold:
            speaker_id = "1"  # 短句说话人
        elif duration > long_threshold:
            speaker_id = "3"  # 长句说话人
        else:
            speaker_id = "2"  # 中等句说话人
        
        subtitle["speaker_id"] = speaker_id
        subtitle["speaker_name"] = f"说话人{speaker_id}"
    
    # 统计说话人
    speaker_count = len(set(s.get("speaker_id") for s in subtitles))
    logger.info(f"✅ 检测到 {speaker_count} 个说话人")
    
    return subtitles

def simple_speaker_detection_by_pattern(subtitles: List[Dict]) -> List[Dict]:
    """
    基于对话模式的简单说话人检测
    假设：小品中通常是轮流对话
    """
    logger.info(f"🎤 基于对话模式进行说话人检测")
    
    if not subtitles:
        return subtitles
    
    # 简单的轮流模式：每3-5句话切换说话人
    current_speaker_id = 1
    sentences_count = 0
    switch_interval = 4  # 每4句话切换说话人
    
    for i, subtitle in enumerate(subtitles):
        sentences_count += 1
        
        # 每隔一定句数切换说话人
        if sentences_count > switch_interval:
            current_speaker_id = (current_speaker_id % 3) + 1  # 在1,2,3之间循环
            sentences_count = 1
            logger.info(f"   第{i+1}句: 切换到说话人{current_speaker_id}")
        
        subtitle["speaker_id"] = str(current_speaker_id)
        subtitle["speaker_name"] = f"说话人{current_speaker_id}"
    
    # 统计说话人
    speaker_count = len(set(s.get("speaker_id") for s in subtitles))
    logger.info(f"✅ 检测到 {speaker_count} 个说话人")
    
    return subtitles

def test_alternative_speaker_detection():
    """测试替代的说话人识别方案"""
    logger.info("🧪 测试替代的说话人识别方案...")
    
    try:
        # 读取之前的测试结果
        with open('multi_speaker_test_result.json', 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        if "subtitles" not in result_data:
            logger.error("❌ 没有找到字幕数据")
            return False
        
        original_subtitles = result_data["subtitles"].copy()
        logger.info(f"📊 原始字幕: {len(original_subtitles)} 句")
        
        # 测试不同的说话人检测方法
        methods = [
            ("基于停顿时间", lambda s: simple_speaker_detection_by_pause(s.copy(), 2.0)),
            ("基于停顿时间(短)", lambda s: simple_speaker_detection_by_pause(s.copy(), 1.0)),
            ("基于语句时长", lambda s: simple_speaker_detection_by_duration(s.copy())),
            ("基于对话模式", lambda s: simple_speaker_detection_by_pattern(s.copy()))
        ]
        
        results = {}
        
        for method_name, method_func in methods:
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 测试方法: {method_name}")
            logger.info(f"{'='*60}")
            
            try:
                processed_subtitles = method_func(original_subtitles)
                
                # 分析结果
                speaker_stats = {}
                for subtitle in processed_subtitles:
                    speaker_id = subtitle.get("speaker_id", "unknown")
                    if speaker_id not in speaker_stats:
                        speaker_stats[speaker_id] = {
                            "count": 0,
                            "examples": []
                        }
                    
                    speaker_stats[speaker_id]["count"] += 1
                    if len(speaker_stats[speaker_id]["examples"]) < 3:
                        speaker_stats[speaker_id]["examples"].append(subtitle.get("text", ""))
                
                logger.info(f"📊 说话人分布:")
                for speaker_id, stats in speaker_stats.items():
                    logger.info(f"   说话人{speaker_id}: {stats['count']} 句话")
                    for i, example in enumerate(stats["examples"]):
                        logger.info(f"      例子{i+1}: {example[:30]}...")
                
                results[method_name] = {
                    "speaker_count": len(speaker_stats),
                    "speaker_stats": speaker_stats,
                    "subtitles": processed_subtitles
                }
                
                # 保存结果
                output_file = f"alternative_speaker_detection_{method_name.replace(' ', '_')}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "method": method_name,
                        "speaker_stats": speaker_stats,
                        "subtitles": processed_subtitles
                    }, f, ensure_ascii=False, indent=2)
                
                logger.info(f"💾 结果已保存到: {output_file}")
                
            except Exception as e:
                logger.error(f"❌ 方法 {method_name} 失败: {e}")
                results[method_name] = None
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def recommend_solution():
    """推荐解决方案"""
    logger.info("💡 推荐解决方案...")
    
    logger.info("🔍 **问题分析:**")
    logger.info("   火山引擎API没有返回说话人信息，可能原因:")
    logger.info("   1. 账户未开通说话人识别功能")
    logger.info("   2. API参数配置问题")
    logger.info("   3. 音频质量或格式问题")
    logger.info("")
    logger.info("🛠️ **解决方案选项:**")
    logger.info("   1. 联系火山引擎技术支持，确认说话人识别功能")
    logger.info("   2. 使用替代的说话人识别服务（如Azure、Google、AWS）")
    logger.info("   3. 使用开源说话人分离工具（如pyannote.audio）")
    logger.info("   4. 使用基于规则的简单说话人检测（临时方案）")
    logger.info("")
    logger.info("🚀 **推荐实施:**")
    logger.info("   短期: 使用基于停顿时间的说话人检测作为临时方案")
    logger.info("   长期: 集成专业的说话人分离服务")

def main():
    """主函数"""
    logger.info("🎯 开始测试替代说话人识别方案...")
    
    tests = [
        ("替代说话人检测", test_alternative_speaker_detection),
        ("解决方案推荐", recommend_solution)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*80}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*80}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 完成")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*80}")
    logger.info("🎯 替代方案测试总结")
    logger.info(f"{'='*80}")
    
    logger.info("🔍 **问题确认:**")
    logger.info("   您说得对，小品视频确实应该有多个说话人")
    logger.info("   火山引擎API的说话人识别功能可能存在问题")
    logger.info("")
    logger.info("📁 **生成的文件:**")
    logger.info("   • alternative_speaker_detection_*.json - 不同方法的检测结果")
    logger.info("")
    logger.info("🎯 **下一步建议:**")
    logger.info("   1. 检查生成的替代检测结果")
    logger.info("   2. 选择最适合的临时方案")
    logger.info("   3. 考虑集成专业的说话人分离服务")

if __name__ == "__main__":
    main()
