#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能音频分割器 - 优先选择中短且流畅的句子片段
"""

import json
import os
import subprocess
import tempfile
from pathlib import Path
import re


def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False


def score_audio_segment(subtitle):
    """
    为音频片段评分，优先选择中短且流畅的句子
    
    Args:
        subtitle: 字幕数据
        
    Returns:
        分数（越高越好）
    """
    text = subtitle['text']
    duration = (subtitle['end_time'] - subtitle['start_time']) / 1000  # 转换为秒
    
    score = 0
    
    # 1. 时长评分 (3-15秒为最佳)
    if 3 <= duration <= 15:
        score += 100
    elif 1.5 <= duration < 3:
        score += 80
    elif 15 < duration <= 25:
        score += 60
    elif duration < 1.5:
        score += 30
    else:
        score += 10
    
    # 2. 文本长度评分 (10-50字为最佳)
    text_length = len(text)
    if 10 <= text_length <= 50:
        score += 80
    elif 5 <= text_length < 10:
        score += 60
    elif 50 < text_length <= 80:
        score += 50
    elif text_length < 5:
        score += 20
    else:
        score += 10
    
    # 3. 句子完整性评分
    # 包含句号、问号、感叹号的完整句子
    if re.search(r'[。！？]', text):
        score += 50
    # 包含逗号的较完整语句
    elif '，' in text:
        score += 30
    
    # 4. 语音流畅性评分
    # 避免包含太多停顿词
    hesitation_words = ['呃', '嗯', '啊', '哎', '哦', '唉']
    hesitation_count = sum(1 for word in hesitation_words if word in text)
    if hesitation_count == 0:
        score += 40
    elif hesitation_count == 1:
        score += 20
    else:
        score -= hesitation_count * 10
    
    # 5. 避免重复词汇过多
    words = list(text)
    unique_words = set(words)
    if len(words) > 0:
        diversity_ratio = len(unique_words) / len(words)
        if diversity_ratio > 0.7:
            score += 30
        elif diversity_ratio > 0.5:
            score += 15
    
    # 6. 内容质量评分
    # 优先选择有意义的对话内容
    meaningful_patterns = [
        r'大夫', r'医生', r'看病', r'治疗', r'心理', r'病情',
        r'老头', r'媳妇', r'家里', r'彩票', r'中奖', r'钱'
    ]
    for pattern in meaningful_patterns:
        if re.search(pattern, text):
            score += 10
    
    return score


def select_best_segments(subtitles, target_duration=60):
    """
    智能选择最佳音频片段
    
    Args:
        subtitles: 说话人的所有字幕列表
        target_duration: 目标总时长（秒）
        
    Returns:
        选中的字幕列表
    """
    # 为每个片段评分
    scored_segments = []
    for subtitle in subtitles:
        score = score_audio_segment(subtitle)
        duration = (subtitle['end_time'] - subtitle['start_time']) / 1000
        scored_segments.append({
            'subtitle': subtitle,
            'score': score,
            'duration': duration
        })
    
    # 按分数排序
    scored_segments.sort(key=lambda x: x['score'], reverse=True)
    
    # 贪心算法选择片段
    selected_segments = []
    total_duration = 0
    
    for segment in scored_segments:
        if total_duration + segment['duration'] <= target_duration:
            selected_segments.append(segment['subtitle'])
            total_duration += segment['duration']
        elif total_duration < target_duration * 0.8:  # 如果还没达到80%，尝试添加较短片段
            if segment['duration'] <= (target_duration - total_duration):
                selected_segments.append(segment['subtitle'])
                total_duration += segment['duration']
    
    # 按时间顺序重新排序
    selected_segments.sort(key=lambda x: x['start_time'])
    
    return selected_segments, total_duration


def create_file_list(audio_files, output_file):
    """创建ffmpeg的文件列表"""
    with open(output_file, 'w', encoding='utf-8') as f:
        for audio_file in audio_files:
            escaped_path = audio_file.replace("'", "'\"'\"'")
            f.write(f"file '{escaped_path}'\n")


def concat_audio_files(audio_files, output_file):
    """使用ffmpeg拼接音频文件"""
    if not audio_files:
        return False
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        list_file = f.name
        create_file_list(audio_files, list_file)
    
    try:
        cmd = [
            'ffmpeg', '-f', 'concat', '-safe', '0', 
            '-i', list_file, '-c', 'copy', '-y', output_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        success = result.returncode == 0
        
        if not success:
            print(f"ffmpeg错误: {result.stderr}")
        
        return success
        
    finally:
        try:
            os.unlink(list_file)
        except:
            pass


def smart_split_speaker_audio(speaker_id, json_file="real_tool_call_result.json", 
                             output_dir="smart_audio", target_duration=60):
    """
    智能分割指定说话人的音频
    """
    if not check_ffmpeg():
        print("错误: 未找到ffmpeg")
        return False
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    print(f"读取文件: {json_file}")
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取指定说话人的字幕
    speaker_subtitles = [s for s in data['subtitles'] if s['speaker_id'] == speaker_id]
    if not speaker_subtitles:
        print(f"错误: 未找到说话人 {speaker_id} 的数据")
        return False
    
    speaker_name = speaker_subtitles[0]['speaker_name']
    audio_segments = data.get('audio_segments', {})
    segments = audio_segments.get(speaker_name, {}).get('segments', [])
    
    print(f"处理说话人 {speaker_id} ({speaker_name})")
    print(f"总片段数: {len(speaker_subtitles)}")
    print(f"目标时长: {target_duration} 秒")
    print("-" * 50)
    
    # 智能选择最佳片段
    selected_subtitles, estimated_duration = select_best_segments(speaker_subtitles, target_duration)
    
    print(f"智能选择了 {len(selected_subtitles)} 个高质量片段")
    print(f"预计总时长: {estimated_duration:.1f} 秒")
    print("-" * 30)
    
    # 收集对应的音频文件
    audio_files_to_concat = []
    actual_duration = 0
    
    for i, subtitle in enumerate(selected_subtitles):
        # 查找匹配的音频文件
        audio_file = None
        start_time_sec = subtitle['start_time'] / 1000
        end_time_sec = subtitle['end_time'] / 1000
        
        for segment in segments:
            if (abs(segment['start_time'] - start_time_sec) < 0.5 and 
                abs(segment['end_time'] - end_time_sec) < 0.5):
                audio_file = segment['file_path']
                break
        
        if not audio_file or not os.path.exists(audio_file):
            print(f"  跳过片段 {i+1}: 音频文件不存在")
            continue
        
        duration = (subtitle['end_time'] - subtitle['start_time']) / 1000
        audio_files_to_concat.append(audio_file)
        actual_duration += duration
        
        # 显示选中的片段信息
        score = score_audio_segment(subtitle)
        print(f"  ✅ 片段 {i+1} (评分:{score:.0f}): {subtitle['text'][:40]}... ({duration:.1f}秒)")
    
    if not audio_files_to_concat:
        print("错误: 没有找到有效的音频文件")
        return False
    
    # 拼接音频文件
    output_file = output_path / f"说话人{speaker_id}_{speaker_name}_智能版_{actual_duration:.1f}秒.wav"
    
    print(f"\n正在拼接 {len(audio_files_to_concat)} 个音频片段...")
    
    if concat_audio_files(audio_files_to_concat, str(output_file)):
        print(f"✅ 智能拼接完成: {output_file}")
        print(f"📊 实际时长: {actual_duration:.1f}秒")
        return True
    else:
        print(f"❌ 拼接失败")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="智能音频分割器")
    parser.add_argument("speaker_id", help="说话人ID")
    parser.add_argument("-j", "--json", default="real_tool_call_result.json",
                       help="JSON文件路径")
    parser.add_argument("-o", "--output", default="smart_audio",
                       help="输出目录")
    parser.add_argument("-d", "--duration", type=int, default=60,
                       help="目标时长（秒）")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json):
        print(f"错误: JSON文件不存在: {args.json}")
        return
    
    smart_split_speaker_audio(
        args.speaker_id, args.json, args.output, args.duration
    )


if __name__ == "__main__":
    main()
