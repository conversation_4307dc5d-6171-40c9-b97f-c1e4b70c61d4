#!/usr/bin/env python3
"""
简化的通义听悟API测试
"""

import logging
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_requirements():
    """检查测试要求"""
    logger.info("🔍 检查测试要求...")
    
    # 检查阿里云AccessKey
    access_key_id = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID")
    access_key_secret = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
    
    if not access_key_id or not access_key_secret:
        logger.error("❌ 缺少阿里云AccessKey配置")
        logger.info("")
        logger.info("📋 配置步骤:")
        logger.info("1. 登录阿里云控制台: https://ram.console.aliyun.com/users")
        logger.info("2. 创建AccessKey")
        logger.info("3. 设置环境变量:")
        logger.info("   export ALIBABA_CLOUD_ACCESS_KEY_ID='your_access_key_id'")
        logger.info("   export ALIBABA_CLOUD_ACCESS_KEY_SECRET='your_access_key_secret'")
        return False
    
    logger.info(f"✅ AccessKey ID: {access_key_id[:10]}...")
    
    # 检查通义听悟AppKey
    app_key = os.getenv("TONGYI_TINGWU_APP_KEY")
    if not app_key:
        logger.warning("⚠️ 缺少通义听悟AppKey")
        logger.info("")
        logger.info("📋 获取AppKey步骤:")
        logger.info("1. 登录阿里云控制台")
        logger.info("2. 搜索'通义听悟'")
        logger.info("3. 进入通义听悟控制台: https://nls-portal.console.aliyun.com/tingwu/projects")
        logger.info("4. 创建项目，获取AppKey")
        logger.info("5. 设置环境变量:")
        logger.info("   export TONGYI_TINGWU_APP_KEY='your_app_key'")
        return False
    
    logger.info(f"✅ AppKey: {app_key[:10]}...")
    return True

def generate_test_commands():
    """生成测试命令"""
    logger.info("📝 生成通义听悟测试命令...")
    
    access_key_id = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID", "YOUR_ACCESS_KEY_ID")
    access_key_secret = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET", "YOUR_ACCESS_KEY_SECRET")
    app_key = os.getenv("TONGYI_TINGWU_APP_KEY", "YOUR_APP_KEY")
    
    video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
    
    # 请求体
    request_body = {
        "AppKey": app_key,
        "Input": {
            "SourceLanguage": "cn",
            "TaskKey": "test_speaker_diarization_manual",
            "FileUrl": video_url
        },
        "Parameters": {
            "Transcription": {
                "DiarizationEnabled": True,
                "Diarization": {
                    "SpeakerCount": 0  # 自动检测说话人数量
                }
            },
            "AutoChaptersEnabled": False,
            "MeetingAssistanceEnabled": False,
            "SummarizationEnabled": False,
            "TranslationEnabled": False,
            "TextPolishEnabled": False
        }
    }
    
    # 保存请求体到文件
    with open('tongyi_request.json', 'w', encoding='utf-8') as f:
        json.dump(request_body, f, ensure_ascii=False, indent=2)
    
    logger.info("💾 请求体已保存到: tongyi_request.json")
    
    # 生成curl命令
    curl_command = f'''
# 1. 提交任务
curl -X PUT "https://tingwu.cn-beijing.aliyuncs.com/openapi/tingwu/v2/tasks?type=offline" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -d @tongyi_request.json

# 2. 查询任务状态 (替换TASK_ID为实际返回的TaskId)
curl -X GET "https://tingwu.cn-beijing.aliyuncs.com/openapi/tingwu/v2/tasks/TASK_ID" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_TOKEN"
'''
    
    # 保存curl命令
    with open('tongyi_curl_commands.sh', 'w', encoding='utf-8') as f:
        f.write(curl_command)
    
    logger.info("💾 curl命令已保存到: tongyi_curl_commands.sh")
    
    # 生成Python SDK测试代码
    python_code = f'''
import os
import json
import time
from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from aliyunsdkcore.auth.credentials import AccessKeyCredential

# 配置
access_key_id = "{access_key_id}"
access_key_secret = "{access_key_secret}"
app_key = "{app_key}"

# 创建客户端
credentials = AccessKeyCredential(access_key_id, access_key_secret)
client = AcsClient(region_id='cn-beijing', credential=credentials)

# 请求体
request_body = {json.dumps(request_body, indent=4)}

# 提交任务
request = CommonRequest()
request.set_accept_format('json')
request.set_domain('tingwu.cn-beijing.aliyuncs.com')
request.set_version('2023-09-30')
request.set_protocol_type('https')
request.set_method('PUT')
request.set_uri_pattern('/openapi/tingwu/v2/tasks')
request.add_header('Content-Type', 'application/json')
request.add_query_param('type', 'offline')
request.set_content(json.dumps(request_body).encode('utf-8'))

response = client.do_action_with_exception(request)
result = json.loads(response)
print("提交结果:", json.dumps(result, indent=2, ensure_ascii=False))

if result.get('Code') == '0':
    task_id = result['Data']['TaskId']
    print(f"任务ID: {{task_id}}")
    
    # 轮询查询结果
    while True:
        query_request = CommonRequest()
        query_request.set_accept_format('json')
        query_request.set_domain('tingwu.cn-beijing.aliyuncs.com')
        query_request.set_version('2023-09-30')
        query_request.set_protocol_type('https')
        query_request.set_method('GET')
        query_request.set_uri_pattern(f'/openapi/tingwu/v2/tasks/{{task_id}}')
        
        query_response = client.do_action_with_exception(query_request)
        query_result = json.loads(query_response)
        
        status = query_result['Data']['TaskStatus']
        print(f"任务状态: {{status}}")
        
        if status == 'COMPLETED':
            print("任务完成!")
            print(json.dumps(query_result, indent=2, ensure_ascii=False))
            break
        elif status == 'FAILED':
            print("任务失败!")
            break
        else:
            time.sleep(30)  # 等待30秒后再查询
'''
    
    # 保存Python代码
    with open('tongyi_test_sdk.py', 'w', encoding='utf-8') as f:
        f.write(python_code)
    
    logger.info("💾 Python SDK测试代码已保存到: tongyi_test_sdk.py")

def show_next_steps():
    """显示下一步操作"""
    logger.info("\n" + "="*80)
    logger.info("🎯 通义听悟测试准备完成")
    logger.info("="*80)
    
    logger.info("\n📋 下一步操作:")
    logger.info("1. 🔑 配置阿里云AccessKey和通义听悟AppKey")
    logger.info("2. 📦 安装阿里云SDK:")
    logger.info("   pip install aliyun-python-sdk-core")
    logger.info("3. 🧪 运行测试:")
    logger.info("   python tongyi_test_sdk.py")
    logger.info("")
    logger.info("📁 生成的文件:")
    logger.info("   • tongyi_request.json - API请求体")
    logger.info("   • tongyi_curl_commands.sh - curl测试命令")
    logger.info("   • tongyi_test_sdk.py - Python SDK测试代码")
    logger.info("")
    logger.info("🔍 关键测试点:")
    logger.info("   • 检查返回结果中是否有SpeakerId字段")
    logger.info("   • 验证是否能识别出多个说话人")
    logger.info("   • 对比与火山引擎的效果差异")

def main():
    """主函数"""
    logger.info("🎯 通义听悟API测试准备...")
    
    # 检查要求
    if check_requirements():
        logger.info("✅ 配置检查通过，可以直接运行测试")
    else:
        logger.info("⚠️ 需要先完成配置")
    
    # 生成测试文件
    generate_test_commands()
    
    # 显示下一步
    show_next_steps()

if __name__ == "__main__":
    main()
