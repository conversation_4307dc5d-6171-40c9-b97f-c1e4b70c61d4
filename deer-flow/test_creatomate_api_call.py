#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实Creatomate API调用测试

展示完整的API调用流程和返回结果
"""

import sys
import json
import asyncio
import logging
import requests
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CreatomateAPITester:
    """Creatomate API真实调用测试器"""
    
    def __init__(self, api_key=None):
        # 从配置中获取API密钥
        self.api_key = api_key
        self.base_url = "https://api.creatomate.com/v1"
        
        if not self.api_key:
            print("⚠️ 未配置API密钥，将使用模拟模式")
            self.mock_mode = True
        else:
            self.mock_mode = False
    
    def create_render(self, json_config):
        """创建渲染任务"""
        print("🎬 创建Creatomate渲染任务...")
        
        if self.mock_mode:
            return self._mock_create_render(json_config)
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/renders",
                headers=headers,
                json=json_config,
                timeout=30
            )
            
            print(f"📡 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 渲染任务创建成功")
                return result
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 网络请求失败: {str(e)}")
            return None
    
    def _mock_create_render(self, json_config):
        """模拟API创建渲染任务"""
        print("🎭 模拟模式：生成真实格式的API响应")
        
        # 模拟真实的API响应格式
        mock_response = {
            "id": "e7b3c4f1-2a9d-4c8e-b1f6-3d5a7e9c2b4f",
            "status": "queued",
            "created_at": "2024-01-15T10:30:00Z",
            "template_id": None,
            "template_tags": {},
            "source": json_config.get("source", {}),
            "output": None,
            "snapshot_time": None,
            "render_scale": 1.0,
            "webhook_url": None,
            "error": None
        }
        
        return mock_response
    
    def get_render_status(self, render_id):
        """获取渲染状态"""
        print(f"📊 查询渲染状态: {render_id}")
        
        if self.mock_mode:
            return self._mock_get_status(render_id)
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
        }
        
        try:
            response = requests.get(
                f"{self.base_url}/renders/{render_id}",
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 状态查询出错: {str(e)}")
            return None
    
    def _mock_get_status(self, render_id):
        """模拟获取渲染状态"""
        # 模拟渲染进度
        import random
        
        statuses = ["queued", "rendering", "succeeded"]
        status = random.choice(statuses)
        
        mock_status = {
            "id": render_id,
            "status": status,
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:31:30Z",
            "progress": 0.85 if status == "rendering" else (1.0 if status == "succeeded" else 0.0),
            "source": {},
            "output": {
                "url": "https://creatomate-renders.s3.amazonaws.com/example-output.mp4",
                "width": 1920,
                "height": 1080,
                "duration": 7.0,
                "file_size": 2048576,
                "format": "mp4"
            } if status == "succeeded" else None,
            "snapshot_time": None,
            "render_scale": 1.0,
            "webhook_url": None,
            "error": None
        }
        
        return mock_status
    
    def monitor_render_progress(self, render_id, max_wait_time=300):
        """监控渲染进度"""
        print(f"⏳ 开始监控渲染进度 (最长等待{max_wait_time}秒)...")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status_info = self.get_render_status(render_id)
            
            if not status_info:
                print("❌ 无法获取状态信息")
                break
            
            status = status_info.get("status", "unknown")
            progress = status_info.get("progress", 0)
            
            print(f"📊 状态: {status}, 进度: {progress*100:.1f}%")
            
            if status == "succeeded":
                print("🎉 渲染完成！")
                return status_info
            elif status == "failed":
                error = status_info.get("error", "未知错误")
                print(f"❌ 渲染失败: {error}")
                return status_info
            
            # 等待5秒后再次查询
            time.sleep(5)
        
        print("⏰ 监控超时")
        return None

async def test_complete_api_workflow():
    """测试完整的API工作流程"""
    print("🚀 完整Creatomate API工作流程测试")
    print("="*60)
    
    try:
        # 先生成AI JSON
        from src.config.configuration import Configuration
        from src.tools.video.creatomate_json_agent import convert_to_creatomate_json
        
        config = Configuration()
        
        # 测试输入
        test_input = {
            "scenes": [
                {
                    "duration": 3,
                    "background_video": "https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/30399726600-1-192.mp4",
                    "audio": "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/deerflow_assets/赵本山小品之相当有钱_爱给网_aigei_com.mp3", 
                    "subtitle": "现在有钱"
                },
                {
                    "duration": 4,
                    "background_video": "继续猩猩",
                    "overlay_video": {"url": "792685ef-1ad9-4ed4-80f1-f66d0055e74f", "position": "right"},
                    "subtitle": "瞅着穿的相当有钱"
                }
            ]
        }
        
        print("🧠 步骤1: AI生成JSON配置")
        ai_json = await convert_to_creatomate_json(config, test_input)
        
        if not ai_json:
            print("❌ AI JSON生成失败")
            return
        
        print("✅ AI JSON生成成功")
        print("📋 生成的JSON配置:")
        print(json.dumps(ai_json, ensure_ascii=False, indent=2)[:1000] + "..." if len(json.dumps(ai_json, ensure_ascii=False, indent=2)) > 1000 else json.dumps(ai_json, ensure_ascii=False, indent=2))
        
        print("\n" + "="*60)
        print("🎬 步骤2: 调用Creatomate API")
        
        # 初始化API测试器（模拟模式）
        api_tester = CreatomateAPITester()
        
        # 创建渲染任务
        render_result = api_tester.create_render(ai_json)
        
        if not render_result:
            print("❌ 渲染任务创建失败")
            return
        
        render_id = render_result.get("id")
        print(f"✅ 渲染任务已创建")
        print(f"📋 任务详情:")
        print(f"• 任务ID: {render_id}")
        print(f"• 状态: {render_result.get('status', '未知')}")
        print(f"• 创建时间: {render_result.get('created_at', '未知')}")
        
        print("\n" + "="*60)
        print("⏳ 步骤3: 监控渲染进度")
        
        # 监控渲染进度
        final_result = api_tester.monitor_render_progress(render_id)
        
        if final_result and final_result.get("status") == "succeeded":
            print("\n" + "="*60)
            print("🎉 步骤4: 渲染成功！获取结果")
            
            output = final_result.get("output", {})
            print("📋 视频输出信息:")
            print(f"• 下载URL: {output.get('url', '无')}")
            print(f"• 分辨率: {output.get('width', '?')}x{output.get('height', '?')}")
            print(f"• 时长: {output.get('duration', '?')}秒")
            print(f"• 文件大小: {output.get('file_size', 0)/1024/1024:.1f}MB")
            print(f"• 格式: {output.get('format', '?')}")
            
            print("\n🔗 完整API响应:")
            print(json.dumps(final_result, ensure_ascii=False, indent=2))
            
            return final_result
        else:
            print("❌ 渲染未成功完成")
            return None
            
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        logger.error(f"详细错误: {str(e)}", exc_info=True)
        return None

def test_api_response_format():
    """测试和展示API响应格式"""
    print("\n📋 Creatomate API响应格式说明")
    print("="*50)
    
    # 展示真实的API响应格式
    example_responses = {
        "创建渲染任务响应": {
            "id": "e7b3c4f1-2a9d-4c8e-b1f6-3d5a7e9c2b4f",
            "status": "queued",
            "created_at": "2024-01-15T10:30:00Z",
            "template_id": None,
            "template_tags": {},
            "source": {"output_format": "mp4", "width": 1920, "height": 1080},
            "output": None,
            "progress": 0.0,
            "webhook_url": None,
            "error": None
        },
        "渲染中状态响应": {
            "id": "e7b3c4f1-2a9d-4c8e-b1f6-3d5a7e9c2b4f",
            "status": "rendering",
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:31:30Z",
            "progress": 0.65,
            "output": None,
            "error": None
        },
        "渲染完成响应": {
            "id": "e7b3c4f1-2a9d-4c8e-b1f6-3d5a7e9c2b4f",
            "status": "succeeded",
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:32:45Z",
            "progress": 1.0,
            "output": {
                "url": "https://creatomate-renders.s3.amazonaws.com/e7b3c4f1-2a9d-4c8e-b1f6-3d5a7e9c2b4f.mp4",
                "width": 1920,
                "height": 1080,
                "duration": 7.0,
                "file_size": 2048576,
                "format": "mp4",
                "thumbnail": "https://creatomate-renders.s3.amazonaws.com/e7b3c4f1-2a9d-4c8e-b1f6-3d5a7e9c2b4f-thumb.jpg"
            },
            "error": None
        }
    }
    
    for title, response in example_responses.items():
        print(f"\n📊 {title}:")
        print(json.dumps(response, ensure_ascii=False, indent=2))

async def main():
    """主测试函数"""
    print("🎬 Creatomate真实API调用测试")
    print("="*60)
    print("展示完整的API调用流程和返回结果")
    print("="*60)
    
    # 测试完整工作流程
    result = await test_complete_api_workflow()
    
    # 展示API响应格式
    test_api_response_format()
    
    print("\n" + "="*60)
    if result:
        print("🎉 API测试完成！")
        print("✅ 您可以看到:")
        print("  • AI生成的完整JSON配置")
        print("  • API创建任务的响应")
        print("  • 渲染进度监控过程")
        print("  • 最终视频输出信息")
    else:
        print("⚠️ 测试部分完成")
        print("💡 建议配置真实API密钥以获得完整体验")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
