#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频人物特写切片器 - 为每个关键人物生成特写片段
"""

import json
import os
import subprocess
from pathlib import Path
import re


def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False


def score_video_segment(subtitle, character_role):
    """
    为视频片段评分，选择最适合特写的片段
    
    Args:
        subtitle: 字幕数据
        character_role: 角色类型 (doctor, wife, patient, other)
        
    Returns:
        分数（越高越好），-1表示排除
    """
    text = subtitle['text']
    duration = (subtitle['end_time'] - subtitle['start_time']) / 1000
    
    # 基础过滤
    if duration < 3 or duration > 25:  # 3-25秒为合适的特写时长
        return -1
    
    if len(text) < 5:  # 文本太短
        return -1
    
    score = 0
    
    # 1. 时长评分 (5-15秒最佳)
    if 5 <= duration <= 15:
        score += 100
    elif 3 <= duration < 5:
        score += 80
    elif 15 < duration <= 20:
        score += 60
    else:
        score += 30
    
    # 2. 根据角色特征评分
    if character_role == "doctor":  # 心理医生 (说话人1)
        doctor_patterns = [
            r'大夫', r'医生', r'治疗', r'看病', r'诊所', r'心理',
            r'病情', r'症状', r'广告', r'村长', r'赵大宝'
        ]
        for pattern in doctor_patterns:
            if re.search(pattern, text):
                score += 20
        
        # 医生的长独白很有特色
        if duration > 15:
            score += 30
            
    elif character_role == "wife":  # 患者家属/妻子 (说话人3)
        wife_patterns = [
            r'老头', r'媳妇', r'家里', r'彩票', r'中奖', r'钱',
            r'病情', r'医院', r'大夫', r'告诉', r'治疗'
        ]
        for pattern in wife_patterns:
            if re.search(pattern, text):
                score += 25
        
        # 妻子解释病情的片段很重要
        if '彩票' in text or '中奖' in text:
            score += 40
            
    elif character_role == "patient":  # 患者 (说话人4)
        patient_patterns = [
            r'媳妇', r'心里', r'怎么回事', r'大夫', r'病',
            r'抽过去', r'钱', r'彩票'
        ]
        for pattern in patient_patterns:
            if re.search(pattern, text):
                score += 25
        
        # 患者的情感表达
        if '心里' in text or '怎么回事' in text:
            score += 35
    
    # 3. 对话质量评分
    if re.search(r'[。！？]', text):
        score += 30
    
    # 4. 情感表达评分
    emotional_indicators = ['！', '？', '呀', '哎', '妈呀']
    for indicator in emotional_indicators:
        if indicator in text:
            score += 15
    
    # 5. 避免过多停顿词
    hesitation_words = ['呃', '嗯', '啊', '哎', '哦', '唉']
    hesitation_count = sum(1 for word in hesitation_words if word in text)
    if hesitation_count > 2:
        score -= hesitation_count * 10
    
    return score


def get_character_role(speaker_id, subtitles):
    """根据说话内容判断角色类型"""
    speaker_texts = [s['text'] for s in subtitles if s['speaker_id'] == speaker_id]
    all_text = ' '.join(speaker_texts)
    
    # 根据关键词判断角色
    if '广告' in all_text and '诊所' in all_text and '村长' in all_text:
        return "doctor"  # 心理医生
    elif '老头' in all_text and '彩票' in all_text and '中奖' in all_text:
        return "wife"    # 患者家属/妻子
    elif '媳妇' in all_text and '心里' in all_text:
        return "patient" # 患者
    else:
        return "other"   # 其他角色


def select_character_clips(subtitles, speaker_id, character_role, num_clips=5):
    """为指定角色选择最佳特写片段"""
    speaker_subtitles = [s for s in subtitles if s['speaker_id'] == speaker_id]
    
    # 为每个片段评分
    scored_segments = []
    for subtitle in speaker_subtitles:
        score = score_video_segment(subtitle, character_role)
        if score > 0:  # 只保留有效片段
            duration = (subtitle['end_time'] - subtitle['start_time']) / 1000
            scored_segments.append({
                'subtitle': subtitle,
                'score': score,
                'duration': duration
            })
    
    # 按分数排序并选择前N个
    scored_segments.sort(key=lambda x: x['score'], reverse=True)
    selected = scored_segments[:num_clips]
    
    # 按时间顺序重新排序
    selected.sort(key=lambda x: x['subtitle']['start_time'])
    
    return [s['subtitle'] for s in selected]


def extract_video_clip(input_video, start_time, end_time, output_file):
    """使用ffmpeg提取视频片段"""
    start_seconds = start_time / 1000
    end_seconds = end_time / 1000
    duration = end_seconds - start_seconds
    
    cmd = [
        'ffmpeg', '-i', input_video,
        '-ss', str(start_seconds),
        '-t', str(duration),
        '-c:v', 'libx264',  # 视频编码
        '-c:a', 'aac',      # 音频编码
        '-preset', 'fast',   # 编码速度
        '-crf', '23',       # 质量设置
        '-y', output_file   # 覆盖输出文件
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"提取视频片段时出错: {e}")
        return False


def create_character_clips(json_file, input_video, output_dir="character_clips", clips_per_character=5):
    """为每个角色创建特写片段"""
    
    if not check_ffmpeg():
        print("错误: 未找到ffmpeg")
        return False
    
    if not os.path.exists(input_video):
        print(f"错误: 视频文件不存在: {input_video}")
        return False
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 读取JSON文件
    print(f"读取文件: {json_file}")
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    subtitles = data['subtitles']
    speaker_info = data['speaker_info']
    
    print(f"视频时长: {data['media_info']['duration']:.1f}秒")
    print(f"发现 {len(speaker_info)} 个说话人")
    print("-" * 60)
    
    # 为每个说话人创建角色片段
    for speaker_id, speaker_name in speaker_info.items():
        print(f"\n处理说话人 {speaker_id} ({speaker_name})")
        
        # 判断角色类型
        character_role = get_character_role(speaker_id, subtitles)
        role_names = {
            "doctor": "心理医生",
            "wife": "患者家属",
            "patient": "患者",
            "other": "其他角色"
        }
        role_name = role_names.get(character_role, "未知角色")
        
        print(f"  角色类型: {role_name}")
        
        # 选择最佳片段
        selected_clips = select_character_clips(subtitles, speaker_id, character_role, clips_per_character)
        
        if not selected_clips:
            print(f"  警告: 没有找到合适的片段")
            continue
        
        print(f"  选择了 {len(selected_clips)} 个特写片段")
        
        # 创建角色专用目录
        character_dir = output_path / f"说话人{speaker_id}_{role_name}"
        character_dir.mkdir(exist_ok=True)
        
        # 提取每个片段
        for i, clip in enumerate(selected_clips, 1):
            start_time = clip['start_time']
            end_time = clip['end_time']
            duration = (end_time - start_time) / 1000
            
            output_file = character_dir / f"特写片段{i:02d}_{duration:.1f}秒.mp4"
            
            print(f"    提取片段 {i}: {clip['text'][:30]}... ({duration:.1f}秒)")
            
            if extract_video_clip(input_video, start_time, end_time, str(output_file)):
                print(f"      ✅ 已保存: {output_file.name}")
            else:
                print(f"      ❌ 提取失败: {output_file.name}")
    
    print(f"\n完成! 所有特写片段已保存到: {output_path}")
    return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="视频人物特写切片器")
    parser.add_argument("input_video", help="输入视频文件路径")
    parser.add_argument("-j", "--json", default="real_tool_call_result.json",
                       help="JSON文件路径")
    parser.add_argument("-o", "--output", default="character_clips",
                       help="输出目录")
    parser.add_argument("-n", "--num-clips", type=int, default=5,
                       help="每个角色的片段数量")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json):
        print(f"错误: JSON文件不存在: {args.json}")
        return
    
    create_character_clips(args.json, args.input_video, args.output, args.num_clips)


if __name__ == "__main__":
    main()
