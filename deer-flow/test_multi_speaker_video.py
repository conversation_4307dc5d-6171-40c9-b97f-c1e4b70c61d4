#!/usr/bin/env python3
"""
测试多说话人视频的说话人识别功能
"""

import logging
import sys
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_multi_speaker_video():
    """测试多说话人视频"""
    logger.info("🎭 测试多说话人视频...")
    
    # 新的测试视频URL
    video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入配置和工具
        from config.configuration import Configuration
        from tools.audio.video_subtitle_extraction import get_video_subtitle_extraction_tool
        
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 创建工具
        tool = get_video_subtitle_extraction_tool(config)
        
        if tool is None:
            logger.error("❌ 工具创建失败")
            return False
        
        logger.info("✅ 视频字幕提取工具创建成功")
        logger.info(f"📹 测试视频: {video_url}")
        
        # 准备测试参数 - 启用说话人识别和COS存储
        test_params = {
            "media_url": video_url,
            "language": "zh-CN",
            "auto_speaker_identification": True,
            "output_format": "json",
            "precision_level": "sentence",
            "extract_audio_segments": True,
            "save_to_cos": True,
            "cos_bucket_prefix": "test-multi-speaker"
        }
        
        logger.info("🚀 开始多说话人视频字幕提取...")
        logger.info("📋 参数:")
        for key, value in test_params.items():
            logger.info(f"   {key}: {value}")
        
        # 调用工具
        result = tool.invoke(test_params)
        
        logger.info("✅ 视频字幕提取完成")
        
        # 解析结果
        if isinstance(result, str):
            result_data = json.loads(result)
        else:
            result_data = result
        
        # 分析说话人识别结果
        logger.info("\n" + "="*80)
        logger.info("🎤 说话人识别结果分析")
        logger.info("="*80)
        
        # 检查说话人信息
        if "speaker_info" in result_data:
            speaker_info = result_data["speaker_info"]
            logger.info(f"👥 识别到的说话人数量: {len(speaker_info)}")
            
            for speaker_id, speaker_name in speaker_info.items():
                logger.info(f"   {speaker_id}: {speaker_name}")
            
            if len(speaker_info) > 1:
                logger.info("🎉 成功识别到多个说话人！")
            else:
                logger.warning("⚠️ 只识别到一个说话人")
        
        # 分析字幕中的说话人分布
        if "subtitles" in result_data:
            subtitles = result_data["subtitles"]
            speaker_stats = {}
            
            for subtitle in subtitles:
                speaker_id = subtitle.get("speaker_id", "unknown")
                speaker_name = subtitle.get("speaker_name", "unknown")
                
                if speaker_id not in speaker_stats:
                    speaker_stats[speaker_id] = {
                        "name": speaker_name,
                        "count": 0,
                        "examples": []
                    }
                
                speaker_stats[speaker_id]["count"] += 1
                
                # 收集前3个例子
                if len(speaker_stats[speaker_id]["examples"]) < 3:
                    speaker_stats[speaker_id]["examples"].append(subtitle.get("text", ""))
            
            logger.info(f"💬 字幕中的说话人分布:")
            for speaker_id, stats in speaker_stats.items():
                logger.info(f"   {speaker_id} ({stats['name']}): {stats['count']} 句话")
                for i, example in enumerate(stats["examples"]):
                    logger.info(f"      例子{i+1}: {example[:50]}...")
        
        # 分析音频片段
        if "audio_segments" in result_data:
            audio_segments = result_data["audio_segments"]
            logger.info(f"🎵 音频片段分析:")
            
            for speaker_name, speaker_data in audio_segments.items():
                if isinstance(speaker_data, dict):
                    segment_count = speaker_data.get("segment_count", 0)
                    total_duration = speaker_data.get("total_duration", 0)
                    logger.info(f"   {speaker_name}: {segment_count}个片段, 总时长{total_duration:.1f}秒")
        
        # 检查COS存储
        if "cos_urls" in result_data:
            cos_urls = result_data["cos_urls"]
            logger.info(f"☁️ COS存储:")
            
            if "json_result" in cos_urls:
                logger.info(f"   📄 JSON结果: {cos_urls['json_result']}")
            
            if "audio_segments" in cos_urls:
                audio_cos = cos_urls["audio_segments"]
                for speaker_name, segments in audio_cos.items():
                    logger.info(f"   🎤 {speaker_name}: {len(segments)} 个音频文件")
        
        # 保存结果
        output_file = "multi_speaker_test_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        logger.info(f"\n💾 测试结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 多说话人视频测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_raw_api_response():
    """测试新视频的火山引擎API原始返回"""
    logger.info("🔍 测试新视频的火山引擎API原始返回...")
    
    video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入必要的模块
        from tools.audio.video_subtitle_extraction import (
            _process_media_input, _submit_subtitle_task, _query_subtitle_result
        )
        from config.configuration import Configuration
        
        config = Configuration.from_runnable_config()
        
        # 获取API密钥
        api_key = os.getenv("VOLCENGINE_SUBTITLE_KEY")
        appid = os.getenv("VOLCENGINE_SUBTITLE_APPID")
        
        if not api_key or not appid:
            logger.error("❌ 缺少火山引擎API配置")
            return False
        
        # 处理媒体输入
        logger.info("📹 处理媒体输入...")
        media_result = _process_media_input(video_url)
        
        if not media_result["success"]:
            logger.error(f"❌ 媒体处理失败: {media_result['error']}")
            return False
        
        audio_url = media_result["audio_url"]
        logger.info(f"🎵 音频URL: {audio_url}")
        
        # 创建测试参数
        class TestArgs:
            def __init__(self):
                self.language = "zh-CN"
                self.auto_speaker_identification = True
                self.speaker_mapping = None
        
        test_args = TestArgs()
        
        # 提交字幕任务
        logger.info("📤 提交字幕任务...")
        submit_result = _submit_subtitle_task(api_key, appid, audio_url, test_args)
        
        if not submit_result["success"]:
            logger.error(f"❌ 任务提交失败: {submit_result['error']}")
            return False
        
        task_id = submit_result["task_id"]
        logger.info(f"✅ 任务提交成功: {task_id}")
        
        # 查询结果
        logger.info("📥 查询字幕结果...")
        query_result = _query_subtitle_result(api_key, appid, task_id)
        
        if not query_result["success"]:
            logger.error(f"❌ 查询结果失败: {query_result['error']}")
            return False
        
        raw_data = query_result["data"]
        logger.info("✅ 查询结果成功")
        
        # 保存原始数据
        with open("multi_speaker_raw_response.json", 'w', encoding='utf-8') as f:
            json.dump(raw_data, f, ensure_ascii=False, indent=2)
        logger.info("💾 原始数据已保存到: multi_speaker_raw_response.json")
        
        # 快速分析说话人信息
        logger.info("\n" + "="*60)
        logger.info("🔍 快速说话人分析")
        logger.info("="*60)
        
        speaker_ids = set()
        utterance_count = 0
        
        if "utterances" in raw_data:
            utterances = raw_data["utterances"]
            utterance_count = len(utterances)
            
            for utterance in utterances:
                # 检查utterance级别的speaker
                if "attribute" in utterance and "speaker" in utterance["attribute"]:
                    speaker_id = utterance["attribute"]["speaker"]
                    speaker_ids.add(speaker_id)
                
                # 检查words级别的speaker
                if "words" in utterance:
                    for word in utterance["words"]:
                        if "attribute" in word and "speaker" in word["attribute"]:
                            word_speaker = word["attribute"]["speaker"]
                            speaker_ids.add(word_speaker)
        
        logger.info(f"📊 总utterances: {utterance_count}")
        logger.info(f"🎤 发现的说话人ID: {speaker_ids}")
        logger.info(f"👥 说话人数量: {len(speaker_ids)}")
        
        if len(speaker_ids) > 1:
            logger.info("🎉 这个视频有多个说话人！")
        elif len(speaker_ids) == 1:
            logger.info("ℹ️ 这个视频只有一个说话人")
        else:
            logger.warning("⚠️ 没有检测到说话人信息")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 原始API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("🎯 开始多说话人视频测试...")
    logger.info("目标: 测试新视频的说话人识别功能")
    
    tests = [
        ("完整工具测试", test_multi_speaker_video),
        ("原始API测试", test_raw_api_response)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*80}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*80}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 成功")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*80}")
    logger.info("🎯 多说话人视频测试总结")
    logger.info(f"{'='*80}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试成功")
    
    if success_count >= 1:
        logger.info("\n🎭 **多说话人测试完成！**")
        logger.info("")
        logger.info("📁 **生成的文件:**")
        logger.info("   • multi_speaker_test_result.json - 完整工具测试结果")
        logger.info("   • multi_speaker_raw_response.json - 火山引擎API原始返回")
        logger.info("")
        logger.info("🔍 **分析要点:**")
        logger.info("   • 检查是否识别到多个说话人")
        logger.info("   • 对比两个视频的差异")
        logger.info("   • 验证说话人识别功能是否正常工作")

if __name__ == "__main__":
    main()
