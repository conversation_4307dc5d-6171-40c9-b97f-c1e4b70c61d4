#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门增强女主角（患者家属，说话人3）的音频
"""

from audio_enhancer import enhance_speaker_audio
import os

def main():
    """增强女主角音频"""
    print("=" * 60)
    print("音频增强工具 - 专门处理女主角（患者家属）的声音")
    print("=" * 60)
    
    # 检查JSON文件
    json_file = "real_tool_call_result.json"
    if not os.path.exists(json_file):
        print(f"错误: 找不到文件 {json_file}")
        return
    
    print("正在处理说话人3（患者家属，女主角）的音频...")
    print("使用中等强度降噪和音质增强...")
    print("-" * 60)
    
    try:
        # 处理说话人3，使用中等增强级别
        success = enhance_speaker_audio(
            speaker_id="3",
            json_file=json_file,
            output_dir="enhanced_audio",
            target_duration=60,
            enhancement_level="medium"
        )
        
        if success:
            print("\n" + "=" * 60)
            print("✅ 女主角音频增强完成！")
            print("📁 增强后的文件保存在: enhanced_audio/")
            print("🎵 文件名: 说话人3_主持人_增强版_XX秒.wav")
            print("=" * 60)
            
            # 提供不同增强级别的选项
            print("\n💡 如果还有杂音，可以尝试更强的增强级别:")
            print("python audio_enhancer.py 3 -l strong")
            print("\n💡 如果声音变得不自然，可以尝试轻度增强:")
            print("python audio_enhancer.py 3 -l light")
        else:
            print("\n❌ 音频增强失败，请检查错误信息")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
