#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频增强器 - 专门处理杂音和提升音质
"""

import json
import os
import subprocess
import tempfile
from pathlib import Path


def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False


def enhance_audio_file(input_file, output_file, enhancement_level="medium"):
    """
    增强单个音频文件
    
    Args:
        input_file: 输入音频文件路径
        output_file: 输出音频文件路径
        enhancement_level: 增强级别 (light, medium, strong)
    """
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        return False
    
    # 根据增强级别设置不同的滤镜参数
    if enhancement_level == "light":
        # 轻度增强 - 保持自然
        filters = [
            "highpass=f=80",           # 去除80Hz以下低频噪音
            "lowpass=f=8000",          # 去除8kHz以上高频噪音
            "volume=1.2"               # 轻微增加音量
        ]
    elif enhancement_level == "medium":
        # 中度增强 - 平衡效果和自然度
        filters = [
            "highpass=f=100",          # 去除100Hz以下低频噪音
            "lowpass=f=7000",          # 去除7kHz以上高频噪音
            "equalizer=f=1000:width_type=h:width=200:g=2",  # 增强1kHz人声频段
            "equalizer=f=3000:width_type=h:width=500:g=1.5", # 增强3kHz清晰度
            "volume=1.3"               # 适度增加音量
        ]
    else:  # strong
        # 强度增强 - 最大化降噪和增强
        filters = [
            "highpass=f=120",          # 去除120Hz以下低频噪音
            "lowpass=f=6000",          # 去除6kHz以上高频噪音
            "equalizer=f=800:width_type=h:width=200:g=3",   # 增强800Hz
            "equalizer=f=1500:width_type=h:width=300:g=2.5", # 增强1.5kHz
            "equalizer=f=3000:width_type=h:width=500:g=2",   # 增强3kHz
            "volume=1.5"               # 较大幅度增加音量
        ]
    
    # 构建ffmpeg命令
    filter_chain = ",".join(filters)
    cmd = [
        'ffmpeg', '-i', input_file,
        '-af', filter_chain,
        '-c:a', 'pcm_s16le',  # 使用16位PCM编码保证质量
        '-ar', '44100',       # 44.1kHz采样率
        '-y', output_file
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return True
        else:
            print(f"ffmpeg错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"处理音频时出错: {e}")
        return False


def enhance_speaker_audio(speaker_id, json_file="real_tool_call_result.json", 
                         output_dir="enhanced_audio", target_duration=60,
                         enhancement_level="medium"):
    """
    增强指定说话人的音频
    
    Args:
        speaker_id: 说话人ID
        json_file: JSON文件路径
        output_dir: 输出目录
        target_duration: 目标时长（秒）
        enhancement_level: 增强级别
    """
    # 检查ffmpeg
    if not check_ffmpeg():
        print("错误: 未找到ffmpeg")
        return False
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 创建临时目录用于处理中间文件
    temp_dir = output_path / "temp"
    temp_dir.mkdir(exist_ok=True)
    
    # 读取JSON文件
    print(f"读取文件: {json_file}")
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取指定说话人的字幕
    speaker_subtitles = [s for s in data['subtitles'] if s['speaker_id'] == speaker_id]
    if not speaker_subtitles:
        print(f"错误: 未找到说话人 {speaker_id} 的数据")
        return False
    
    # 按时间排序
    speaker_subtitles.sort(key=lambda x: x['start_time'])
    
    # 获取音频片段信息
    audio_segments = data.get('audio_segments', {})
    speaker_name = speaker_subtitles[0]['speaker_name']
    segments = audio_segments.get(speaker_name, {}).get('segments', [])
    
    print(f"处理说话人 {speaker_id} ({speaker_name})")
    print(f"增强级别: {enhancement_level}")
    print(f"可用片段数: {len(segments)}")
    print("-" * 50)
    
    # 处理每个音频片段
    enhanced_files = []
    current_duration = 0
    
    for i, subtitle in enumerate(speaker_subtitles):
        if current_duration >= target_duration:
            break
        
        # 查找匹配的音频文件
        audio_file = None
        start_time_sec = subtitle['start_time'] / 1000
        end_time_sec = subtitle['end_time'] / 1000
        
        for segment in segments:
            if (abs(segment['start_time'] - start_time_sec) < 0.5 and 
                abs(segment['end_time'] - end_time_sec) < 0.5):
                audio_file = segment['file_path']
                break
        
        if not audio_file or not os.path.exists(audio_file):
            print(f"  跳过片段 {i+1}: 音频文件不存在")
            continue
        
        # 增强音频片段
        enhanced_file = temp_dir / f"enhanced_{i+1:03d}.wav"
        print(f"  处理片段 {i+1}: {subtitle['text'][:30]}...")
        
        if enhance_audio_file(audio_file, str(enhanced_file), enhancement_level):
            enhanced_files.append(str(enhanced_file))
            
            # 估算时长（简单估算）
            duration = (subtitle['end_time'] - subtitle['start_time']) / 1000
            current_duration += duration
            print(f"    ✅ 增强完成 ({duration:.1f}秒)")
        else:
            print(f"    ❌ 增强失败")
    
    if not enhanced_files:
        print("错误: 没有成功处理任何音频片段")
        return False
    
    # 拼接增强后的音频文件
    print(f"\n拼接 {len(enhanced_files)} 个增强后的音频片段...")
    
    # 创建文件列表
    list_file = temp_dir / "filelist.txt"
    with open(list_file, 'w', encoding='utf-8') as f:
        for audio_file in enhanced_files:
            escaped_path = audio_file.replace("'", "'\"'\"'")
            f.write(f"file '{escaped_path}'\n")
    
    # 拼接文件
    final_output = output_path / f"说话人{speaker_id}_{speaker_name}_增强版_{current_duration:.1f}秒.wav"
    
    cmd = [
        'ffmpeg', '-f', 'concat', '-safe', '0', 
        '-i', str(list_file), '-c', 'copy', '-y', str(final_output)
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print(f"✅ 增强完成: {final_output}")
        
        # 清理临时文件
        try:
            for temp_file in enhanced_files:
                os.unlink(temp_file)
            os.unlink(list_file)
            temp_dir.rmdir()
        except:
            pass
        
        return True
    else:
        print(f"❌ 拼接失败: {result.stderr}")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="音频增强器")
    parser.add_argument("speaker_id", help="说话人ID")
    parser.add_argument("-j", "--json", default="real_tool_call_result.json",
                       help="JSON文件路径")
    parser.add_argument("-o", "--output", default="enhanced_audio",
                       help="输出目录")
    parser.add_argument("-d", "--duration", type=int, default=60,
                       help="目标时长（秒）")
    parser.add_argument("-l", "--level", choices=["light", "medium", "strong"],
                       default="medium", help="增强级别")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json):
        print(f"错误: JSON文件不存在: {args.json}")
        return
    
    enhance_speaker_audio(
        args.speaker_id, args.json, args.output, 
        args.duration, args.level
    )


if __name__ == "__main__":
    main()
