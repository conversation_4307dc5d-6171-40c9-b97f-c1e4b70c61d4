#!/usr/bin/env python3
"""
测试通义听悟适配器
验证转换后的格式是否与现有工具兼容
"""

import logging
import sys
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tongyi_adapter():
    """测试通义听悟适配器"""
    logger.info("🎯 测试通义听悟适配器...")
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入适配器
        from tools.audio.tongyi_tingwu_adapter import TongyiTingwuAdapter
        
        # 配置信息
        access_key_id = "LTAI5tBTpMAPNJ3Z6jpZyTSo"
        access_key_secret = "******************************"
        app_key = "wbp1hepOKEWDiQEC"
        
        # 创建适配器
        adapter = TongyiTingwuAdapter(access_key_id, access_key_secret, app_key)
        logger.info("✅ 适配器创建成功")
        
        # 测试视频URL
        video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
        
        # 处理音频
        logger.info("🚀 开始处理音频...")
        result = adapter.process_audio(video_url)
        
        if result["success"]:
            logger.info("✅ 音频处理成功！")
            
            # 保存结果
            volcengine_format_data = result["data"]
            original_data = result["original_data"]
            
            # 保存转换后的格式
            with open('tongyi_volcengine_format.json', 'w', encoding='utf-8') as f:
                json.dump(volcengine_format_data, f, ensure_ascii=False, indent=2)
            logger.info("💾 火山引擎格式数据已保存到: tongyi_volcengine_format.json")
            
            # 保存原始格式
            with open('tongyi_original_format.json', 'w', encoding='utf-8') as f:
                json.dump(original_data, f, ensure_ascii=False, indent=2)
            logger.info("💾 通义听悟原始数据已保存到: tongyi_original_format.json")
            
            # 分析转换结果
            analyze_conversion_result(volcengine_format_data)
            
            return True
            
        else:
            logger.error(f"❌ 音频处理失败: {result['error']}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_conversion_result(volcengine_data):
    """分析转换结果"""
    logger.info("\n" + "="*80)
    logger.info("📊 转换结果分析")
    logger.info("="*80)
    
    try:
        utterances = volcengine_data.get("utterances", [])
        logger.info(f"📝 总utterances数量: {len(utterances)}")
        
        # 统计说话人
        speaker_stats = {}
        for utterance in utterances:
            speaker_id = utterance.get("attribute", {}).get("speaker", "unknown")
            text = utterance.get("text", "")
            
            if speaker_id not in speaker_stats:
                speaker_stats[speaker_id] = {
                    "count": 0,
                    "total_chars": 0,
                    "examples": []
                }
            
            speaker_stats[speaker_id]["count"] += 1
            speaker_stats[speaker_id]["total_chars"] += len(text)
            
            if len(speaker_stats[speaker_id]["examples"]) < 3:
                speaker_stats[speaker_id]["examples"].append(text)
        
        logger.info(f"👥 说话人数量: {len(speaker_stats)}")
        
        # 详细统计
        for speaker_id, stats in speaker_stats.items():
            percentage = (stats["count"] / len(utterances)) * 100
            avg_chars = stats["total_chars"] / stats["count"] if stats["count"] > 0 else 0
            
            logger.info(f"\n🎤 说话人 {speaker_id}:")
            logger.info(f"   utterances数量: {stats['count']}")
            logger.info(f"   占比: {percentage:.1f}%")
            logger.info(f"   平均字数: {avg_chars:.1f}")
            logger.info(f"   示例:")
            for i, example in enumerate(stats["examples"]):
                logger.info(f"      {i+1}. {example[:50]}...")
        
        # 检查数据格式兼容性
        logger.info(f"\n🔍 格式兼容性检查:")
        
        sample_utterance = utterances[0] if utterances else {}
        required_fields = ["start_time", "end_time", "text", "attribute"]
        
        for field in required_fields:
            if field in sample_utterance:
                logger.info(f"   ✅ {field}: {type(sample_utterance[field])}")
            else:
                logger.warning(f"   ❌ 缺少字段: {field}")
        
        # 检查说话人字段
        if "attribute" in sample_utterance and "speaker" in sample_utterance["attribute"]:
            logger.info(f"   ✅ speaker字段: {sample_utterance['attribute']['speaker']}")
        else:
            logger.warning(f"   ❌ 缺少speaker字段")
        
        logger.info(f"\n🎯 结论:")
        if len(speaker_stats) > 1:
            logger.info("✅ 成功转换为火山引擎兼容格式")
            logger.info("✅ 保留了说话人分离信息")
            logger.info("✅ 可以直接用于现有工具")
        else:
            logger.warning("⚠️ 只有一个说话人，可能需要检查")
            
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")

def test_with_existing_tool():
    """测试与现有工具的兼容性"""
    logger.info("\n" + "="*80)
    logger.info("🧪 测试与现有工具的兼容性")
    logger.info("="*80)
    
    try:
        # 读取转换后的数据
        with open('tongyi_volcengine_format.json', 'r', encoding='utf-8') as f:
            volcengine_data = json.load(f)
        
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入现有工具的处理函数
        from tools.audio.video_subtitle_extraction import _process_volcengine_data
        
        # 模拟现有工具的处理
        logger.info("🔄 使用现有工具处理转换后的数据...")
        
        # 这里需要模拟现有工具的参数
        class MockArgs:
            def __init__(self):
                self.auto_speaker_identification = True
                self.speaker_mapping = None
        
        mock_args = MockArgs()
        
        # 调用现有的处理函数
        processed_result = _process_volcengine_data(volcengine_data, mock_args)
        
        logger.info("✅ 现有工具处理成功！")
        logger.info(f"📊 处理结果: {len(processed_result.get('subtitles', []))} 条字幕")
        
        # 保存兼容性测试结果
        with open('compatibility_test_result.json', 'w', encoding='utf-8') as f:
            json.dump(processed_result, f, ensure_ascii=False, indent=2)
        logger.info("💾 兼容性测试结果已保存到: compatibility_test_result.json")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 兼容性测试失败: {e}")
        logger.info("这可能是因为现有工具的函数签名有变化，需要调整")
        return False

def main():
    """主函数"""
    logger.info("🎯 通义听悟适配器测试")
    logger.info("="*60)
    
    # 测试适配器
    adapter_success = test_tongyi_adapter()
    
    if adapter_success:
        # 测试兼容性
        compatibility_success = test_with_existing_tool()
        
        logger.info(f"\n{'='*80}")
        logger.info("🎯 测试总结")
        logger.info(f"{'='*80}")
        
        if adapter_success and compatibility_success:
            logger.info("🎉 适配器测试完全成功！")
            logger.info("✅ 通义听悟数据成功转换为火山引擎格式")
            logger.info("✅ 与现有工具完全兼容")
            logger.info("✅ 可以直接集成到现有系统中")
            
            logger.info("\n📁 生成的文件:")
            logger.info("   • tongyi_volcengine_format.json - 转换后的火山引擎格式")
            logger.info("   • tongyi_original_format.json - 通义听悟原始格式")
            logger.info("   • compatibility_test_result.json - 兼容性测试结果")
            
            logger.info("\n🚀 下一步:")
            logger.info("   1. 集成适配器到现有工具")
            logger.info("   2. 添加配置选项")
            logger.info("   3. 部署测试")
            
        elif adapter_success:
            logger.info("✅ 适配器基本功能正常")
            logger.warning("⚠️ 与现有工具的兼容性需要调整")
            logger.info("🔧 需要微调接口适配")
        else:
            logger.error("❌ 适配器测试失败")
            logger.info("🔧 需要检查配置和网络")
    
    else:
        logger.error("❌ 适配器创建失败，无法继续测试")

if __name__ == "__main__":
    main()
