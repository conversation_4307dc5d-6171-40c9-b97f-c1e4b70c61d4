#!/usr/bin/env python3
"""
测试通义听悟API的说话人分离功能
"""

import logging
import sys
import os
import json
import time
import requests

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tongyi_tingwu_api():
    """测试通义听悟API"""
    logger.info("🎯 测试通义听悟API说话人分离功能...")
    
    # 检查环境变量
    access_key_id = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID")
    access_key_secret = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
    app_key = os.getenv("TONGYI_TINGWU_APP_KEY")  # 需要在控制台创建
    
    if not access_key_id or not access_key_secret:
        logger.error("❌ 缺少阿里云AccessKey配置")
        logger.info("请设置环境变量:")
        logger.info("export ALIBABA_CLOUD_ACCESS_KEY_ID='your_access_key_id'")
        logger.info("export ALIBABA_CLOUD_ACCESS_KEY_SECRET='your_access_key_secret'")
        logger.info("export TONGYI_TINGWU_APP_KEY='your_app_key'")
        return False
    
    if not app_key:
        logger.warning("⚠️ 缺少通义听悟AppKey，使用测试AppKey")
        app_key = "test_app_key"  # 需要替换为真实的AppKey
    
    logger.info(f"🔑 Access Key ID: {access_key_id[:10]}...")
    logger.info(f"🆔 App Key: {app_key}")
    
    # 测试视频URL
    video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
    
    try:
        # 安装阿里云SDK
        try:
            from aliyunsdkcore.client import AcsClient
            from aliyunsdkcore.request import CommonRequest
            from aliyunsdkcore.auth.credentials import AccessKeyCredential
        except ImportError:
            logger.error("❌ 缺少阿里云SDK，正在安装...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "aliyun-python-sdk-core"])
            from aliyunsdkcore.client import AcsClient
            from aliyunsdkcore.request import CommonRequest
            from aliyunsdkcore.auth.credentials import AccessKeyCredential
        
        # 创建客户端
        credentials = AccessKeyCredential(access_key_id, access_key_secret)
        client = AcsClient(region_id='cn-beijing', credential=credentials)
        
        # 构建请求体
        request_body = {
            'AppKey': app_key,
            'Input': {
                'SourceLanguage': 'cn',
                'TaskKey': f'test_speaker_diarization_{int(time.time())}',
                'FileUrl': video_url
            },
            'Parameters': {
                # 语音识别 - 开启说话人分离
                'Transcription': {
                    'DiarizationEnabled': True,
                    'Diarization': {
                        'SpeakerCount': 0  # 0表示自动检测说话人数量
                    }
                },
                # 可选：开启其他功能
                'AutoChaptersEnabled': False,
                'MeetingAssistanceEnabled': False,
                'SummarizationEnabled': False,
                'TranslationEnabled': False,
                'TextPolishEnabled': False
            }
        }
        
        logger.info("📋 请求参数:")
        logger.info(json.dumps(request_body, indent=2, ensure_ascii=False))
        
        # 创建请求
        request = CommonRequest()
        request.set_accept_format('json')
        request.set_domain('tingwu.cn-beijing.aliyuncs.com')
        request.set_version('2023-09-30')
        request.set_protocol_type('https')
        request.set_method('PUT')
        request.set_uri_pattern('/openapi/tingwu/v2/tasks')
        request.add_header('Content-Type', 'application/json')
        request.add_query_param('type', 'offline')
        request.set_content(json.dumps(request_body).encode('utf-8'))
        
        # 提交任务
        logger.info("📤 提交通义听悟任务...")
        response = client.do_action_with_exception(request)
        response_data = json.loads(response)
        
        logger.info("✅ 任务提交成功")
        logger.info(f"📥 响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        
        if response_data.get('Code') != '0':
            logger.error(f"❌ 任务提交失败: {response_data.get('Message')}")
            return False
        
        task_id = response_data['Data']['TaskId']
        logger.info(f"🆔 任务ID: {task_id}")
        
        # 轮询查询结果
        logger.info("🔄 开始轮询查询结果...")
        max_wait_time = 1800  # 30分钟
        poll_interval = 30    # 30秒查询一次
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # 查询任务状态
            query_request = CommonRequest()
            query_request.set_accept_format('json')
            query_request.set_domain('tingwu.cn-beijing.aliyuncs.com')
            query_request.set_version('2023-09-30')
            query_request.set_protocol_type('https')
            query_request.set_method('GET')
            query_request.set_uri_pattern(f'/openapi/tingwu/v2/tasks/{task_id}')
            query_request.add_header('Content-Type', 'application/json')
            
            query_response = client.do_action_with_exception(query_request)
            query_data = json.loads(query_response)
            
            task_status = query_data['Data']['TaskStatus']
            logger.info(f"📊 任务状态: {task_status}")
            
            if task_status == 'COMPLETED':
                logger.info("🎉 任务完成！")
                
                # 保存完整结果
                with open('tongyi_tingwu_result.json', 'w', encoding='utf-8') as f:
                    json.dump(query_data, f, ensure_ascii=False, indent=2)
                logger.info("💾 完整结果已保存到: tongyi_tingwu_result.json")
                
                # 分析结果
                analyze_tongyi_result(query_data)
                return True
                
            elif task_status == 'FAILED':
                logger.error(f"❌ 任务失败: {query_data['Data'].get('ErrorMessage', 'Unknown error')}")
                return False
                
            elif task_status == 'ONGOING':
                elapsed = time.time() - start_time
                logger.info(f"⏳ 任务进行中... (已等待 {elapsed:.0f}秒)")
                time.sleep(poll_interval)
            else:
                logger.warning(f"⚠️ 未知任务状态: {task_status}")
                time.sleep(poll_interval)
        
        logger.error("❌ 任务超时")
        return False
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_tongyi_result(result_data):
    """分析通义听悟的结果"""
    logger.info("\n" + "="*80)
    logger.info("🎤 通义听悟说话人分离结果分析")
    logger.info("="*80)
    
    try:
        data = result_data['Data']
        
        # 检查是否有转写结果
        if 'Result' in data and 'Transcription' in data['Result']:
            transcription_url = data['Result']['Transcription']
            logger.info(f"📄 转写结果URL: {transcription_url}")
            
            # 下载转写结果
            logger.info("📥 下载转写结果...")
            response = requests.get(transcription_url)
            
            if response.status_code == 200:
                transcription_data = response.json()
                
                # 保存转写结果
                with open('tongyi_transcription_result.json', 'w', encoding='utf-8') as f:
                    json.dump(transcription_data, f, ensure_ascii=False, indent=2)
                logger.info("💾 转写结果已保存到: tongyi_transcription_result.json")
                
                # 分析说话人信息
                analyze_speaker_info(transcription_data)
                
            else:
                logger.error(f"❌ 下载转写结果失败: {response.status_code}")
        else:
            logger.warning("⚠️ 没有找到转写结果")
            
    except Exception as e:
        logger.error(f"❌ 分析结果失败: {e}")

def analyze_speaker_info(transcription_data):
    """分析说话人信息"""
    logger.info("\n📊 说话人分离详细分析:")
    
    try:
        # 检查是否有说话人信息
        speaker_stats = {}
        total_sentences = 0
        
        # 分析utterances
        if 'Result' in transcription_data:
            utterances = transcription_data['Result'].get('Sentences', [])
            total_sentences = len(utterances)
            
            logger.info(f"📝 总句子数: {total_sentences}")
            
            for sentence in utterances:
                # 检查说话人信息
                speaker_id = sentence.get('SpeakerId', 'unknown')
                text = sentence.get('Text', '')
                
                if speaker_id not in speaker_stats:
                    speaker_stats[speaker_id] = {
                        'count': 0,
                        'examples': []
                    }
                
                speaker_stats[speaker_id]['count'] += 1
                
                # 收集前3个例子
                if len(speaker_stats[speaker_id]['examples']) < 3:
                    speaker_stats[speaker_id]['examples'].append(text)
            
            # 输出统计结果
            logger.info(f"👥 检测到的说话人数量: {len(speaker_stats)}")
            
            for speaker_id, stats in speaker_stats.items():
                logger.info(f"\n🎤 说话人 {speaker_id}:")
                logger.info(f"   句子数量: {stats['count']}")
                logger.info(f"   占比: {stats['count']/total_sentences*100:.1f}%")
                logger.info(f"   示例:")
                for i, example in enumerate(stats['examples']):
                    logger.info(f"      {i+1}. {example[:50]}...")
            
            if len(speaker_stats) > 1:
                logger.info("\n🎉 成功检测到多个说话人！")
            else:
                logger.warning("\n⚠️ 只检测到一个说话人")
        
        else:
            logger.warning("⚠️ 转写结果格式不符合预期")
            
    except Exception as e:
        logger.error(f"❌ 分析说话人信息失败: {e}")

def main():
    """主函数"""
    logger.info("🎯 开始测试通义听悟API...")
    logger.info("目标: 验证说话人分离功能是否有效")
    
    logger.info("\n📋 测试步骤:")
    logger.info("1. 检查API配置")
    logger.info("2. 提交音视频转写任务")
    logger.info("3. 轮询查询结果")
    logger.info("4. 分析说话人分离效果")
    
    success = test_tongyi_tingwu_api()
    
    logger.info(f"\n{'='*80}")
    logger.info("🎯 通义听悟测试总结")
    logger.info(f"{'='*80}")
    
    if success:
        logger.info("✅ 测试成功完成")
        logger.info("")
        logger.info("📁 生成的文件:")
        logger.info("   • tongyi_tingwu_result.json - 完整API响应")
        logger.info("   • tongyi_transcription_result.json - 转写结果详情")
        logger.info("")
        logger.info("🔍 下一步:")
        logger.info("   1. 检查说话人分离效果")
        logger.info("   2. 对比与火山引擎的差异")
        logger.info("   3. 决定是否集成通义听悟")
    else:
        logger.error("❌ 测试失败")
        logger.info("")
        logger.info("🔧 可能的解决方案:")
        logger.info("   1. 检查阿里云AccessKey配置")
        logger.info("   2. 在通义听悟控制台创建AppKey")
        logger.info("   3. 确认账户权限和余额")

if __name__ == "__main__":
    main()
