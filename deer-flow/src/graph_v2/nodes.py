# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Nodes for the V2 graph.

This file defines the primary node for the new architecture: the Master Agent node.
"""
from src.agents.factory import create_agent

# --- Tool Imports for Expert Initialization ---
from src.config.configuration import Configuration
from src.tools.image.text_to_image import get_jmeng_image_generation_tool
from src.tools.image.flux_image_edit import get_flux_image_edit_tool
from src.tools.image.multi_image_edit import get_multi_image_flux_edit_tool
from src.tools.audio.music_generation import get_suno_music_generation_tool
from src.tools.audio.text_to_speech import get_text_to_speech_tool, get_text_to_voice_tool
from src.tools.audio.voice_clone import get_voice_clone_tool
from src.tools.audio.multi_speaker_tts import get_multi_speaker_tts_tool
from src.tools.audio.video_subtitle_extraction import get_video_subtitle_extraction_tool
from src.tools.video import get_image_to_video_tool, get_text_to_video_tool, get_video_synthesis_tool
# --- End Tool Imports ---

from src.config.agents import AGENT_LLM_MAP
from src.llms.llm import get_llm_by_type
from src.prompts.template import apply_prompt_template
from langgraph.prebuilt import create_react_agent
from langchain_core.runnables import RunnableConfig

from .types import State

def master_agent_node(state: State, config: RunnableConfig):
    """
    The central node of the V2 architecture. It invokes the Master Agent.
    """
    # 延迟导入避免循环导入
    from src.tools.experts import get_visual_expert_tool, get_audio_expert_tool, get_video_expert_tool
    from src.tools.planning import create_plan, initialize_planning_tools
    from src.tools.state_management import get_plan_status, get_next_step, report_step_completion
    from src.tools.template_tools import use_template
    from src.tools.understanding import (
        get_multimodal_understanding_tool,
        get_image_understanding_tool,
        get_video_understanding_tool,
        get_audio_understanding_tool
    )

    llm = get_llm_by_type(AGENT_LLM_MAP["master_agent"])
    app_config = Configuration.from_runnable_config(config)

    # Initialize the planning tools with the chosen LLM
    initialize_planning_tools(llm)

    # --- Create Low-Level Tools ---
    _visual_tools = [
        get_jmeng_image_generation_tool(app_config),
        get_flux_image_edit_tool(app_config),
        get_multi_image_flux_edit_tool(app_config)
    ]
    _audio_tools = [
        get_suno_music_generation_tool(app_config),
        get_text_to_speech_tool(app_config),
        get_voice_clone_tool(app_config),
        get_text_to_voice_tool(app_config),
        get_multi_speaker_tts_tool(app_config),
        get_video_subtitle_extraction_tool(app_config)
    ]
    _video_tools = [
        get_image_to_video_tool(app_config),
        get_text_to_video_tool(app_config),
        get_video_synthesis_tool(app_config),
        # get_creatomate_to_video_tool(app_config),
    ]
    # --- End Low-Level Tools ---
    
    # --- Create High-Level Expert Tools with correct low-level tools ---
    expert_tools = [
        get_visual_expert_tool(tools_list=_visual_tools),
        get_audio_expert_tool(tools_list=_audio_tools),
        get_video_expert_tool(tools_list=_video_tools),
    ]
    # --- End High-Level Expert Tools ---

    # --- Create Understanding Tools ---
    understanding_tools = [
        get_multimodal_understanding_tool(app_config),
        get_image_understanding_tool(app_config),
        get_video_understanding_tool(app_config),
        get_audio_understanding_tool(app_config),
    ]
    # --- End Understanding Tools ---

    # Combine all tools for the master agent (simplified to 8 core tools)
    planning_tools = [create_plan]
    template_tools = [use_template]
    state_management_tools = [get_plan_status, get_next_step, report_step_completion]
    all_tools = expert_tools + planning_tools + template_tools + state_management_tools + understanding_tools
    
    # We use the create_react_agent factory, which is what our create_agent delegates to.
    # This gives us the ReAct loop behavior we want for the Master Agent.
    master_agent = create_react_agent(
        name="master_agent",
        model=llm,
        tools=all_tools,
        prompt=lambda state: apply_prompt_template("master_agent_prompt", state),
    )
    
    result = master_agent.invoke(state)
    # The result from the ReAct agent includes not just the final messages,
    # but also any updates to the other state fields, like `plan` and `past_steps`.
    # We need to return the whole result to update the graph's state.
    return result 