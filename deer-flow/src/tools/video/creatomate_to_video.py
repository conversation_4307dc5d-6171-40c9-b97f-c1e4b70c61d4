# import asyncio
# import requests
# import time
# import logging
# import os
# from functools import partial
# from typing import Optional
# from pydantic.v1 import BaseModel, Field
# from langchain_core.tools import StructuredTool
# from src.config.configuration import Configuration


# # 配置日志
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# logger = logging.getLogger(__name__)

# # API 配置
# CREATOMATE_API_KEY = "017ad773fca84ddea445ccbe72ea4d5d699a7e796b915e8a262db7c6e338bda44b83ebfaedba1186ac6196eebc74ab26"
# CREATOMATE_API_URL = "https://api.creatomate.com/v1/renders"

# # 下载配置
# DOWNLOAD_PATH = "/Users/<USER>/Downloads/test"

# class GenerateVideoFromImageInput(BaseModel):
#     payload_json: str = Field(..., description="用于生成视频的payload_json，必须符合creatomate平台要求的JSON对象，具体JSON配置请参考：https://creatomate.com/docs 文档")


# def wait_for_render_completion(render_id: str, headers: dict, auto_download: bool = True, max_wait: int = 600):
#     """
#     等待渲染完成并返回最终URL
    
#     Args:
#         render_id: 渲染任务ID
#         headers: 请求头
#         auto_download: 是否自动下载到本地
#         max_wait: 最大等待时间（秒）
            
#         Returns:
#         渲染完成的视频URL或本地路径，失败返回None
#     """
#     logger.info(f"⏳ 开始等待渲染完成 (任务ID: {render_id})...")
#     logger.info(f"⏰ 最大等待时间: {max_wait} 秒")
    
#     start_time = time.time()
#     check_count = 0
    
#     while time.time() - start_time < max_wait:
#         check_count += 1
#         elapsed_time = time.time() - start_time
        
#         try:
#             logger.info(f"🔍 第 {check_count} 次检查状态 (已等待 {elapsed_time:.1f} 秒)...")
            
#             # 获取渲染状态
#             status_response = requests.get(
#                 f"{CREATOMATE_API_URL}/{render_id}",
#                 headers=headers,
#                 timeout=30
#             )
            
#             if status_response.status_code == 200:
#                 try:
#                     render_info = status_response.json()
#                     logger.info(f" 状态响应: {render_info}")
                    
#                     # 处理可能的列表响应
#                     if isinstance(render_info, list) and len(render_info) > 0:
#                         render_info = render_info[0]
                    
#                     if isinstance(render_info, dict):
#                         status = render_info.get('status')
#                         logger.info(f" 当前状态: {status}")
                        
#                         # 检查是否成功完成
#                         if status == 'succeeded':
#                             video_url = render_info.get('url')
#                             if video_url:
#                                 logger.info(f"🎉 渲染成功完成！")
#                                 logger.info(f"🔗 视频URL: {video_url}")
#                                 logger.info(f"⏱️  总耗时: {elapsed_time:.1f} 秒")
                                
#                                 # 显示视频信息
#                                 file_size = render_info.get('file_size', 0)
#                                 duration = render_info.get('duration', 0)
#                                 width = render_info.get('width', 0)
#                                 height = render_info.get('height', 0)
                                
#                                 logger.info(f"📊 视频信息:")
#                                 logger.info(f"   文件大小: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
#                                 logger.info(f"   ⏱️  时长: {duration} 秒")
#                                 logger.info(f"   📐 尺寸: {width}x{height}")
                                
#                                 # 下载视频到本地
#                                 if auto_download:
#                                     local_path = download_video(video_url, render_id)
#                                     if local_path:
#                                         logger.info(f"💾 视频已下载到: {local_path}")
#                                         return local_path
#                                     else:
#                                         logger.warning("⚠️  下载失败，返回原始URL")
#                                         return video_url
#                                 else:
#                                     return video_url
#                             else:
#                                 logger.error("❌ 渲染成功但未获取到视频URL")
#                                 return None
                                
#                         elif status == 'completed':
#                             # 兼容旧的状态名
#                             video_url = render_info.get('url')
#                             if video_url:
#                                 logger.info(f"🎉 渲染完成！")
#                                 logger.info(f"🔗 视频URL: {video_url}")
#                                 logger.info(f"⏱️  总耗时: {elapsed_time:.1f} 秒")
                                
#                                 # 下载视频到本地
#                                 if auto_download:
#                                     local_path = download_video(video_url, render_id)
#                                     if local_path:
#                                         logger.info(f"💾 视频已下载到: {local_path}")
#                                         return local_path
#                                     else:
#                                         logger.warning("⚠️  下载失败，返回原始URL")
#                                         return video_url
#                                 else:
#                                     return video_url
#                             else:
#                                 logger.error("❌ 渲染完成但未获取到视频URL")
#                                 return None

#                         elif status == 'failed':
#                             error = render_info.get('error', '未知错误')
#                             logger.error(f"❌ 渲染失败: {error}")
#                             return None
                            
#                         elif status in ['pending', 'processing', 'rendering']:
#                             # 继续等待
#                             wait_time = min(10, max_wait - elapsed_time)  # 最多等待10秒
#                             if wait_time > 0:
#                                 logger.info(f"⏸️  等待 {wait_time} 秒后重试...")
#                                 time.sleep(wait_time)
#                             else:
#                                 break
#                         else:
#                             logger.warning(f"⚠️  未知状态: {status}")
#                             time.sleep(10)
#                     else:
#                         logger.error(f"❌ 状态响应格式错误: {type(render_info)}")
#                         time.sleep(10)
                        
#                 except Exception as e:
#                     logger.error(f"❌ 解析状态响应时发生错误: {str(e)}")
#                     time.sleep(10)
#                 else:
#                     logger.error(f"❌ 获取状态失败: {status_response.status_code}")
#                     logger.error(f"错误信息: {status_response.text}")
#                     time.sleep(10)
                
#         except requests.exceptions.Timeout:
#             logger.warning(f"⏰ 请求超时，继续重试...")
#             time.sleep(10)
#         except requests.exceptions.ConnectionError as e:
#             logger.warning(f"🔌 连接错误: {str(e)}")
#             time.sleep(10)
#         except Exception as e:
#             logger.error(f"❌ 检查状态时发生错误: {str(e)}")
#             time.sleep(10)
#     logger.error(f"⏰ 渲染超时，等待时间超过 {max_wait} 秒")
#     return None


# def download_video(video_url: str, render_id: str) -> str:
#     """
#         下载视频到本地
        
#         Args:
#         video_url: 视频URL
#         render_id: 渲染任务ID
            
#         Returns:
#         本地文件路径，失败返回None
#     """
#     try:
#         # 确保下载目录存在
#         os.makedirs(DOWNLOAD_PATH, exist_ok=True)
        
#         # 生成文件名
#         timestamp = int(time.time())
#         filename = f"creatomate_video_{render_id}_{timestamp}.mp4"
#         local_path = os.path.join(DOWNLOAD_PATH, filename)
        
#         logger.info(f"📥 开始下载视频...")
#         logger.info(f"🔗 源URL: {video_url}")
#         logger.info(f"📁 目标路径: {local_path}")
            
#         # 下载视频
#         response = requests.get(video_url, stream=True, timeout=300)
#         response.raise_for_status()
            
#         # 获取文件大小
#         total_size = int(response.headers.get('content-length', 0))
            
#         # 写入文件
#         with open(local_path, 'wb') as f:
#             downloaded = 0
#             for chunk in response.iter_content(chunk_size=8192):
#                 if chunk:
#                     f.write(chunk)
#                     downloaded += len(chunk)
                    
#                     # 显示下载进度
#                     if total_size > 0:
#                         progress = (downloaded / total_size) * 100
#                     logger.info(f" 下载进度: {progress:.1f}% ({downloaded:,}/{total_size:,} bytes)")
        
#         # 验证文件大小
#         actual_size = os.path.getsize(local_path)
#         logger.info(f"✅ 下载完成！")
#         logger.info(f" 文件大小: {actual_size:,} bytes ({actual_size/1024/1024:.1f} MB)")
        
#         return local_path
            
#     except Exception as e:
#         logger.error(f"❌ 下载视频时发生错误: {str(e)}")
#         return None


# def create_video_with_payload(config: Configuration, payload_json: dict, auto_download: bool = True):
#     """
#     使用自定义payload创建视频并等待完成
    
#     Args:
#         payload: 渲染请求的payload数据
#         auto_download: 是否自动下载到本地
        
#     Returns:
#         本地文件路径或视频URL，失败返回None
#     """
#     headers = {
#         'Authorization': f'Bearer {CREATOMATE_API_KEY}',
#         'Content-Type': 'application/json',
#     }
    
#     try:
#         logger.info("🚀 开始创建视频渲染任务...")
#         logger.info(f" 请求数据: {payload_json}")

#         # TODO 校验 payload_json
        
#         # 创建渲染任务
#         response = requests.post(
#             CREATOMATE_API_URL,
#             headers=headers,
#             json=payload_json,
#             timeout=30
#         )
        
#         logger.info(f"📡 响应状态码: {response.status_code}")
#         logger.info(f"📄 响应内容: {response.text}")
        
#         if response.status_code in [200, 201, 202]:
#             try:
#                 render_data = response.json()
#                 logger.info(f"📊 响应数据类型: {type(render_data)}")
#                 logger.info(f" 响应数据: {render_data}")
                
#                 # 检查响应数据类型
#                 if isinstance(render_data, dict):
#                     render_id = render_data.get('id')
#                     status = render_data.get('status')
                    
#                     if render_id:
#                         logger.info(f"✅ 渲染任务创建成功！")
#                         logger.info(f"🆔 任务ID: {render_id}")
#                         logger.info(f"📊 初始状态: {status}")
                        
#                         # 等待渲染完成
#                         return wait_for_render_completion(render_id, headers, auto_download)
#                     else:
#                         logger.error("❌ 响应中未找到任务ID")
#                         return None
                        
#                 elif isinstance(render_data, list):
#                     logger.warning("⚠️  响应是列表格式，尝试获取第一个元素")
#                     if len(render_data) > 0:
#                         first_item = render_data[0]
#                         if isinstance(first_item, dict):
#                             render_id = first_item.get('id')
#                             status = first_item.get('status')
                            
#                             if render_id:
#                                 logger.info(f"✅ 渲染任务创建成功！")
#                                 logger.info(f"🆔 任务ID: {render_id}")
#                                 logger.info(f"📊 初始状态: {status}")
                                
#                                 # 等待渲染完成
#                                 return wait_for_render_completion(render_id, headers, auto_download)
#                             else:
#                                 logger.error("❌ 列表第一个元素中未找到任务ID")
#                                 return None
#                         else:
#                             logger.error("❌ 列表第一个元素不是字典格式")
#                             return None
#                     else:
#                         logger.error("❌ 响应列表为空")
#                         return None
#                 else:
#                     logger.error(f"❌ 未知的响应数据类型: {type(render_data)}")
#                 return None
                
#             except Exception as e:
#                 logger.error(f"❌ 解析响应数据时发生错误: {str(e)}")
#                 return None
#         else:
#             logger.error(f"❌ 创建渲染任务失败: {response.status_code}")
#             logger.error(f"错误信息: {response.text}")
#             return None
    
#     except Exception as e:
#         logger.error(f"❌ 创建渲染任务时发生错误: {str(e)}")
#         return None


# def get_creatomate_to_video_tool(config: Configuration) -> Optional[StructuredTool]:
#     """
#     Creates a high-level tool that performs the entire Image-to-Video generation workflow.
#     """
#     logger.info(f"=======================>>Creatomate生成视频工具初始化 config: {config}")

#     if not all([config.kling_api_base, config.kling_api_key]):
#         logger.warning("Kling I2V tool disabled: KLING_API_BASE or KLING_API_KEY not in config.")
#         return None
    
#     # Create sync wrapper for long-running task
#     def _sync_execute_i2v(**kwargs):
#         try:
#             return asyncio.run(create_video_with_payload(config, **kwargs))
#         except Exception as e:
#             return f"❌ Creatomate生成视频失败: {str(e)}"
    
#     return StructuredTool.from_function(
#         func=_sync_execute_i2v,
#         coroutine=partial(create_video_with_payload, config),
#         name="get_creatomate_to_video",
#         description=(
#             "Creatomate专业视频制作工具 - 基于云端API的完整视频渲染解决方案\n\n"
#             "**功能概述:**\n"
#             "这是一个基于 [Creatomate API](https://creatomate.com/docs) 的企业级视频制作工具，提供从简单模板渲染到复杂多轨道视频合成的全方位服务。工具支持云端渲染，无需本地硬件资源，能够处理专业级的视频制作需求。\n\n"
#             "**输入内容支持:**\n"
#             "1. **模板模式:** 通过 `template_id` 指定预设计模板，使用 `modifications` 对象替换模板中的动态内容\n"
#             "2. **自定义模式:** 通过 `source` 对象提供完整的视频配置，包含输出格式、尺寸、时长和元素列表\n"
#             "3. **元素类型:** 支持视频、音频、文本、图片、形状、组、动画等多种元素类型\n"
#             "4. **资源格式:** 支持HTTPS URL、本地文件路径、Creatomate资源ID等多种资源引用方式\n\n"
#             "**核心能力详解:**\n"
#             "1. **多轨道视频合成:**\n"
#             "   - 支持无限数量的视频轨道，每个轨道可独立控制时间、位置、大小\n"
#             "   - 精确的时间轴控制，支持毫秒级的时间定位和持续时间设置\n"
#             "   - 轨道层级管理，支持元素的叠加顺序和透明度控制\n\n"
#             "2. **专业音频处理:**\n"
#             "   - 多轨道音频混合，支持背景音乐、配音、音效的独立控制\n"
#             "   - 音量控制：支持百分比音量调节（0%-100%）\n"
#             "   - 音频同步：支持音频与视频的精确同步和转录功能\n"
#             "   - 循环播放：支持背景音乐的循环播放设置\n\n"
#             "3. **高级文字系统:**\n"
#             "   - 多行文字支持，可设置字体、大小、颜色、描边、背景\n"
#             "   - 位置控制：支持绝对位置（像素）和相对位置（百分比）\n"
#             "   - 时间控制：精确控制文字的出现时间、持续时间和消失时间\n"
#             "   - 转录功能：支持从视频/音频自动生成字幕并应用样式\n\n"
#             "4. **视觉效果引擎:**\n"
#             "   - 变换属性：位置(x,y)、大小(width,height)、旋转(rotation)、透明度(opacity)\n"
#             "   - 颜色处理：填充色、描边色、背景色，支持RGB、HEX、RGBA格式\n"
#             "   - 滤镜效果：支持多种内置滤镜和自定义滤镜参数\n"
#             "   - 动画系统：支持关键帧动画和预设动画效果\n\n"
#             "5. **输出质量控制:**\n"
#             "   - 格式支持：MP4、MOV、GIF等多种输出格式\n"
#             "   - 分辨率控制：支持自定义输出尺寸（如1920x1080、4K等）\n"
#             "   - 质量设置：支持不同质量等级的渲染选项\n"
#             "   - 时长控制：精确控制输出视频的总时长\n\n"
#             "**技术特性:**\n"
#             "- **云端渲染:** 无需本地GPU，所有渲染在云端完成\n"
#             "- **异步处理:** 支持长时间视频渲染，自动监控进度状态\n"
#             "- **状态监控:** 实时反馈渲染进度（pending→processing→succeeded/failed）\n"
#             "- **自动下载:** 渲染完成后自动下载到指定本地目录\n"
#             "- **错误恢复:** 完善的错误处理和重试机制，支持网络中断恢复\n"
#             "- **资源管理:** 自动管理临时文件和渲染资源\n\n"
#             "**适用场景:**\n"
#             "- **短视频制作:** 抖音、快手、小红书等平台的视频内容\n"
#             "- **营销视频:** 产品演示、广告宣传、品牌推广视频\n"
#             "- **教育培训:** 课程视频、教学演示、知识分享内容\n"
#             "- **企业宣传:** 公司介绍、产品展示、活动记录视频\n"
#             "- **自动化内容:** 批量生成个性化视频内容\n"
#             "- **直播回放:** 直播内容的后期处理和剪辑\n\n"
#             "**使用方式:**\n"
#             "传入符合 Creatomate API 规范的 payload 配置对象，工具会自动：\n"
#             "1. 验证配置参数的有效性\n"
#             "2. 创建云端渲染任务并获取任务ID\n"
#             "3. 实时监控渲染进度和状态变化\n"
#             "4. 渲染完成后自动下载到本地存储\n"
#             "5. 返回本地文件路径或在线视频URL\n\n"
#             "**配置示例:**\n"
#             "```json\n"
#             "{\n"
#             "  \"source\": {\n"
#             "    \"output_format\": \"mp4\",\n"
#             "    \"width\": 1920,\n"
#             "    \"height\": 1080,\n"
#             "    \"duration\": 10,\n"
#             "    \"elements\": [\n"
#             "      {\n"
#             "        \"type\": \"video\",\n"
#             "        \"source\": \"https://example.com/video.mp4\",\n"
#             "        \"volume\": \"0%\"\n"
#             "      },\n"
#             "      {\n"
#             "        \"type\": \"audio\",\n"
#             "        \"source\": \"https://example.com/audio.mp3\",\n"
#             "        \"volume\": \"80%\"\n"
#             "      },\n"
#             "      {\n"
#             "        \"type\": \"text\",\n"
#             "        \"text\": \"标题文字\",\n"
#             "        \"font_size\": 40,\n"
#             "        \"fill_color\": \"#ffffff\"\n"
#             "      }\n"
#             "    ]\n"
#             "  }\n"
#             "}\n"
#             "```\n\n"
#             "**API参考:**\n"
#             "详细配置参数、元素属性、动画效果等请参考 [Creatomate API 文档](https://creatomate.com/docs)"
#         ),
#         args_schema=GenerateVideoFromImageInput,
#     )



# def main():
#     """主函数 - 演示不同的创建方式"""
#     print(" Creatomate 视频制作 - 重构版本")
#     print("=" * 60)
    
#     try:
#         # # 方式1: 使用模板创建视频
#         # print("1️⃣ 使用模板创建视频...")
#         # template_payload = {
#         #     'template_id': 'a060b933-ee96-4e91-bbd7-d478de7b0a5e',
#         #     'modifications': {
#         #         'Text': '是终于通了吗？',
#         #         'Video': 'https://cdn.creatomate.com/demo/drone.mp4'
#         #     }
#         # }
        
#         # result1 = create_video_with_payload(template_payload)
        
#         # if result1:
#         #     print(f"✅ 模板视频创建成功: {result1}")
#         # else:
#         #     print("❌ 模板视频创建失败")
        
#         # 方式2: 使用自定义源配置创建视频
#         # print("\n2️⃣ 使用自定义源配置创建视频...")
#         # source_payload = {
#         #     'output_format': 'mp4',
#         #     'elements': [
#         #         {
#         #             'type': 'video',
#         #             'id': '17ca2169-786f-477f-aaea-4a2598bf24eb',
#         #             'source': 'https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/30399726600-1-192.mp4'
#         #         },
                
#         #         {
#         #             "type": "text",
#         #             "transcript_source": "17ca2169-786f-477f-aaea-4a2598bf24eb",
#         #             "transcript_effect": "highlight",
#         #             "transcript_maximum_length": 14,
#         #             "y": "82%",
#         #             "width": "81%",
#         #             "height": "35%",
#         #             "x_alignment": "50%",
#         #             "y_alignment": "50%",
#         #             "fill_color": "#ffffff",
#         #             "stroke_color": "#000000",
#         #             "stroke_width": "1.6 vmin",
#         #             "font_family": "Montserrat",
#         #             "font_weight": "700",
#         #             "font_size": "9.29 vmin",
#         #             "background_color": "rgba(216,216,216,0)",
#         #             "background_x_padding": "31%",
#         #             "background_y_padding": "17%",
#         #             "background_border_radius": "31%"
#         #         }
#         #     ]
#         # }

#         source_payload = {
#             "source": {
#                 "output_format": "mp4",
#                 "width": 1920,
#                 "height": 1080,
#                 "duration": 7.1,
#                 "elements": [
#                     {
#                         "id": "dd501121-8a3a-4ad2-9012-44eb91d57b14",
#                         "name": "猩猩",
#                         "type": "video",
#                         "track": 1,
#                         "volume": "0%",
#                         "source": "https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/30399726600-1-192.mp4"
#                     },
#                     {
#                         "id": "2e33a672-bceb-4fe4-8d08-acf1ea20f08c",
#                         "name": "哪吒",
#                         "type": "video",
#                         "track": 2,
#                         "time": 3,
#                         "x": "87.338%",
#                         "y": "38.3767%",
#                         "width": "25.3241%",
#                         "height": "76.7533%",
#                         "source": "792685ef-1ad9-4ed4-80f1-f66d0055e74f",
#                         "volume": "0%"
#                     },
#                     {
#                         "id": "86dc95b8-d78f-419e-a65a-2807099d3578",
#                         "name": "宋丹丹",
#                         "type": "audio",
#                         "track": 3,
#                         "time": 0.01,
#                         "source": "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/deerflow_assets/%E8%B5%B5%E6%9C%AC%E5%B1%B1%E5%B0%8F%E5%93%81%E4%B9%8B%E7%9B%B8%E5%BD%93%E6%9C%89%E9%92%B1%20_%E7%88%B1%E7%BB%99%E7%BD%91_aigei_com.mp3"
#                     },
#                     {
#                         "id": "686245a3-0cf3-4df0-bdbf-0f2f6b4a615e",
#                         "type": "text",
#                         "track": 4,
#                         "time": 0,
#                         "duration": 2.57,
#                         "y": "79%",
#                         "text": "现在有钱",
#                         "font_family": "Arial",
#                         "font_size": 40,
#                         "fill_color": "#ffffff",
#                         "stroke_color": "#333333"
#                     },
#                     {
#                         "id": "06bde8f8-8d72-472e-9053-1f19557979de",
#                         "name": "Text-RFW",
#                         "type": "text",
#                         "track": 5,
#                         "time": 2.64,
#                         "duration": 0.44,
#                         "y": "79%",
#                         "text": "哼",
#                         "font_family": "Arial",
#                         "font_size": 40,
#                         "fill_color": "#ffffff",
#                         "stroke_color": "#333333"
#                     },
#                     {
#                         "id": "90827a20-1bca-4e9c-ab42-fca8efcd92ef",
#                         "name": "Text-M3X",
#                         "type": "text",
#                         "track": 6,
#                         "time": 3,
#                         "duration": 1.21,
#                         "y": "79%",
#                         "text": "瞅着穿的",
#                         "font_family": "Arial",
#                         "font_size": 40,
#                         "fill_color": "#ffffff",
#                         "stroke_color": "#333333"
#                     },
#                     {
#                         "id": "8bbf692c-be6b-4b77-bee7-13c755ba8905",
#                         "name": "Text-C4P",
#                         "type": "text",
#                         "track": 7,
#                         "time": 4.21,
#                         "duration": 1.81,
#                         "y": "79%",
#                         "text": "相当有钱",
#                         "font_family": "Arial",
#                         "font_size": 40,
#                         "fill_color": "#ffffff",
#                         "stroke_color": "#333333"
#                     },
#                     {
#                         "id": "67345d1e-7691-42e4-acdb-285b036d62e5",
#                         "name": "Text-B83",
#                         "type": "text",
#                         "track": 8,
#                         "time": 6.08,
#                         "duration": 0.663,
#                         "y": "79%",
#                         "text": "嘿",
#                         "font_family": "Arial",
#                         "font_size": 40,
#                         "fill_color": "#ffffff",
#                         "stroke_color": "#333333"
#                     }
#                 ]
#             }
#         }

#         # result2 = create_video_with_payload(source_payload)
        
#         # if result2:
#         #     print(f"✅ 自定义视频创建成功: {result2}")
#         # else:
#         #     print("❌ 自定义视频创建失败")
        
#         # print("\n🎉 演示完成！")
        
#     except KeyboardInterrupt:
#         print("\n⚠️  用户中断了程序")
#     except Exception as e:
#         print(f"\n❌ 程序执行出错: {str(e)}")

# if __name__ == "__main__":
#     main()