# # Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# # SPDX-License-Identifier: MIT

# """
# Creatomate专业视频制作工具 - DeerFlow标准版本

# 这是一个基于Creatomate API的企业级视频制作工具，遵循DeerFlow的五大黄金法则设计。
# 提供Agent友好的接口，支持从简单模板到复杂自定义视频的全方位制作能力。
# """

# import asyncio
# import requests
# import time
# import logging
# import os
# import json
# import tempfile
# import uuid
# from functools import partial
# from typing import Optional, Dict, Any, List, Literal, Union
# from pathlib import Path

# from pydantic.v1 import BaseModel, Field, validator
# from langchain_core.tools import StructuredTool

# from src.config.configuration import Configuration

# # 配置日志
# logger = logging.getLogger(__name__)

# # === Input Models ===

# class CreatomateVideoInput(BaseModel):
#     """Creatomate视频制作工具输入参数 - Agent友好版本"""
    
#     # 基础配置
#     video_type: Literal["template", "custom", "simple"] = Field(
#         default="simple",
#         description="视频制作模式：template=使用模板, custom=自定义配置, simple=简单快速模式"
#     )
    
#     # 模板模式参数
#     template_id: Optional[str] = Field(
#         default=None,
#         description="模板ID（仅在video_type=template时需要）"
#     )
#     template_variables: Optional[Dict[str, Any]] = Field(
#         default=None,
#         description="模板变量替换（如：{'Text': '标题', 'Video': '视频URL'}）"
#     )
    
#     # 简单模式参数（Agent友好）
#     background_video: Optional[str] = Field(
#         default=None,
#         description="背景视频URL（简单模式）"
#     )
#     background_audio: Optional[str] = Field(
#         default=None,
#         description="背景音频URL（简单模式）"
#     )
#     title_text: Optional[str] = Field(
#         default=None,
#         description="标题文字（简单模式）"
#     )
#     subtitles: Optional[List[Dict[str, Any]]] = Field(
#         default=None,
#         description="字幕列表，格式：[{'text': '文字', 'start': 0, 'duration': 2}]"
#     )
    
#     # 输出设置
#     output_width: int = Field(default=1920, description="输出宽度（像素）")
#     output_height: int = Field(default=1080, description="输出高度（像素）")
#     output_duration: Optional[float] = Field(default=None, description="输出时长（秒，留空自动计算）")
#     output_format: Literal["mp4", "mov", "gif"] = Field(default="mp4", description="输出格式")
    
#     # 自定义模式参数（高级用户）
#     custom_payload: Optional[Union[str, Dict[str, Any]]] = Field(
#         default=None,
#         description="自定义Creatomate配置（JSON字符串或字典对象）"
#     )
    
#     # 下载设置
#     auto_download: bool = Field(default=True, description="是否自动下载到本地")
#     download_path: Optional[str] = Field(default=None, description="下载路径（留空使用系统临时目录）")
    
#     @validator('video_type')
#     def validate_video_type(cls, v):
#         if v not in ['template', 'custom', 'simple']:
#             raise ValueError("video_type必须是：template, custom, simple之一")
#         return v
    
#     @validator('template_id')
#     def validate_template_mode(cls, v, values):
#         video_type = values.get('video_type')
#         if video_type == 'template' and not v:
#             raise ValueError("模板模式下必须提供template_id")
#         return v
    
#     @validator('custom_payload')
#     def validate_custom_mode(cls, v, values):
#         video_type = values.get('video_type')
#         if video_type == 'custom' and not v:
#             raise ValueError("自定义模式下必须提供custom_payload")
#         return v


# # === Core Implementation ===

# class CreatomateVideoProcessor:
#     """Creatomate视频处理器 - 封装所有复杂逻辑"""
    
#     def __init__(self, config: Configuration):
#         self.config = config
#         self.api_key = self._get_api_key()
#         self.api_url = "https://api.creatomate.com/v1/renders"
        
#     def _get_api_key(self) -> str:
#         """获取API密钥，支持多种配置方式"""
#         # 优先级：环境变量 > 配置文件 > 默认值
#         api_key = (
#             os.getenv("CREATOMATE_API_KEY") or 
#             getattr(self.config, 'creatomate_api_key', None) or
#             # 临时兼容旧代码的硬编码密钥（生产环境应移除）
#             "017ad773fca84ddea445ccbe72ea4d5d699a7e796b915e8a262db7c6e338bda44b83ebfaedba1186ac6196eebc74ab26"
#         )
        
#         if not api_key:
#             raise ValueError("Creatomate API密钥未配置。请设置环境变量 CREATOMATE_API_KEY")
        
#         return api_key
    
#     def _get_download_path(self, custom_path: Optional[str] = None) -> str:
#         """获取下载路径"""
#         if custom_path:
#             return custom_path
        
#         # 使用系统临时目录
#         temp_dir = tempfile.gettempdir()
#         download_dir = os.path.join(temp_dir, "deerflow_creatomate")
#         os.makedirs(download_dir, exist_ok=True)
#         return download_dir
    
#     def _build_simple_payload(self, args: CreatomateVideoInput) -> Dict[str, Any]:
#         """构建简单模式的payload - Agent友好"""
#         elements = []
#         track_counter = 1
        
#         # 添加背景视频
#         if args.background_video:
#             elements.append({
#                 "id": f"bg_video_{uuid.uuid4().hex[:8]}",
#                 "type": "video",
#                 "track": track_counter,
#                 "source": args.background_video,
#                 "volume": "0%" if args.background_audio else "50%"  # 有音频时静音视频
#             })
#             track_counter += 1
        
#         # 添加背景音频
#         if args.background_audio:
#             elements.append({
#                 "id": f"bg_audio_{uuid.uuid4().hex[:8]}",
#                 "type": "audio",
#                 "track": track_counter,
#                 "source": args.background_audio,
#                 "volume": "80%"
#             })
#             track_counter += 1
        
#         # 添加标题
#         if args.title_text:
#             elements.append({
#                 "id": f"title_{uuid.uuid4().hex[:8]}",
#                 "type": "text",
#                 "track": track_counter,
#                 "time": 0,
#                 "duration": 3,  # 标题显示3秒
#                 "text": args.title_text,
#                 "y": "20%",  # 顶部位置
#                 "font_family": "Arial",
#                 "font_size": 48,
#                 "font_weight": "bold",
#                 "fill_color": "#ffffff",
#                 "stroke_color": "#000000",
#                 "stroke_width": 2,
#                 "x_alignment": "50%",
#                 "y_alignment": "50%"
#             })
#             track_counter += 1
        
#         # 添加字幕
#         if args.subtitles:
#             for i, subtitle in enumerate(args.subtitles):
#                 elements.append({
#                     "id": f"subtitle_{i}_{uuid.uuid4().hex[:8]}",
#                     "type": "text",
#                     "track": track_counter + i,
#                     "time": subtitle.get("start", 0),
#                     "duration": subtitle.get("duration", 2),
#                     "text": subtitle.get("text", ""),
#                     "y": "80%",  # 底部位置
#                     "font_family": "Arial",
#                     "font_size": 32,
#                     "fill_color": "#ffffff",
#                     "stroke_color": "#000000",
#                     "stroke_width": 1,
#                     "x_alignment": "50%",
#                     "y_alignment": "50%"
#                 })
        
#         return {
#             "source": {
#                 "output_format": args.output_format,
#                 "width": args.output_width,
#                 "height": args.output_height,
#                 "duration": args.output_duration,
#                 "elements": elements
#             }
#         }
    
#     def _build_template_payload(self, args: CreatomateVideoInput) -> Dict[str, Any]:
#         """构建模板模式的payload"""
#         payload = {
#             "template_id": args.template_id
#         }
        
#         if args.template_variables:
#             payload["modifications"] = args.template_variables
            
#         return payload
    
#     def _build_custom_payload(self, args: CreatomateVideoInput) -> Dict[str, Any]:
#         """构建自定义模式的payload"""
#         if isinstance(args.custom_payload, str):
#             try:
#                 return json.loads(args.custom_payload)
#             except json.JSONDecodeError as e:
#                 raise ValueError(f"自定义payload JSON格式错误: {e}")
#         elif isinstance(args.custom_payload, dict):
#             return args.custom_payload
#         else:
#             raise ValueError("custom_payload必须是JSON字符串或字典对象")
    
#     async def _wait_for_completion(self, render_id: str, max_wait: int = 600) -> Optional[str]:
#         """等待渲染完成 - 异步版本"""
#         headers = {
#             'Authorization': f'Bearer {self.api_key}',
#             'Content-Type': 'application/json',
#         }
        
#         logger.info(f"⏳ 开始等待渲染完成 (任务ID: {render_id})...")
#         start_time = time.time()
#         check_count = 0
        
#         while time.time() - start_time < max_wait:
#             check_count += 1
#             elapsed_time = time.time() - start_time
            
#             try:
#                 logger.info(f"🔍 第 {check_count} 次检查状态 (已等待 {elapsed_time:.1f} 秒)...")
                
#                 # 使用requests.get而不是aiohttp（保持与原代码一致）
#                 response = requests.get(
#                     f"{self.api_url}/{render_id}",
#                     headers=headers,
#                     timeout=30
#                 )
                
#                 if response.status_code == 200:
#                     render_info = response.json()
                    
#                     # 处理可能的列表响应
#                     if isinstance(render_info, list) and len(render_info) > 0:
#                         render_info = render_info[0]
                    
#                     if isinstance(render_info, dict):
#                         status = render_info.get('status')
#                         logger.info(f"📊 当前状态: {status}")
                        
#                         if status in ['succeeded', 'completed']:
#                             video_url = render_info.get('url')
#                             if video_url:
#                                 logger.info(f"🎉 渲染成功完成！视频URL: {video_url}")
#                                 return video_url
#                             else:
#                                 logger.error("❌ 渲染成功但未获取到视频URL")
#                                 return None
                                
#                         elif status == 'failed':
#                             error = render_info.get('error', '未知错误')
#                             logger.error(f"❌ 渲染失败: {error}")
#                             return None
                            
#                         elif status in ['pending', 'processing', 'rendering']:
#                             # 继续等待
#                             await asyncio.sleep(min(10, max_wait - elapsed_time))
#                         else:
#                             logger.warning(f"⚠️ 未知状态: {status}")
#                             await asyncio.sleep(10)
#                 else:
#                     logger.error(f"❌ 获取状态失败: {response.status_code}")
#                     await asyncio.sleep(10)
                    
#             except Exception as e:
#                 logger.error(f"❌ 检查状态时发生错误: {e}")
#                 await asyncio.sleep(10)
        
#         logger.error(f"⏰ 渲染超时，等待时间超过 {max_wait} 秒")
#         return None
    
#     async def _download_video(self, video_url: str, download_path: str, render_id: str) -> Optional[str]:
#         """下载视频到本地 - 异步版本"""
#         try:
#             # 生成文件名
#             timestamp = int(time.time())
#             filename = f"creatomate_video_{render_id}_{timestamp}.mp4"
#             local_path = os.path.join(download_path, filename)
            
#             logger.info(f"📥 开始下载视频到: {local_path}")
            
#             # 下载视频
#             response = requests.get(video_url, stream=True, timeout=300)
#             response.raise_for_status()
            
#             # 写入文件
#             with open(local_path, 'wb') as f:
#                 for chunk in response.iter_content(chunk_size=8192):
#                     if chunk:
#                         f.write(chunk)
            
#             # 验证文件
#             actual_size = os.path.getsize(local_path)
#             logger.info(f"✅ 下载完成！文件大小: {actual_size:,} bytes ({actual_size/1024/1024:.1f} MB)")
            
#             return local_path
            
#         except Exception as e:
#             logger.error(f"❌ 下载视频时发生错误: {e}")
#             return None
    
#     async def process_video(self, args: CreatomateVideoInput) -> Dict[str, Any]:
#         """处理视频创建请求 - 主入口"""
#         try:
#             # 1. 根据模式构建payload
#             if args.video_type == "template":
#                 payload = self._build_template_payload(args)
#             elif args.video_type == "custom":
#                 payload = self._build_custom_payload(args)
#             else:  # simple
#                 payload = self._build_simple_payload(args)
            
#             logger.info(f"🚀 开始创建视频渲染任务，模式: {args.video_type}")
            
#             # 2. 创建渲染任务
#             headers = {
#                 'Authorization': f'Bearer {self.api_key}',
#                 'Content-Type': 'application/json',
#             }
            
#             response = requests.post(
#                 self.api_url,
#                 headers=headers,
#                 json=payload,
#                 timeout=30
#             )
            
#             if response.status_code not in [200, 201, 202]:
#                 return {
#                     "success": False,
#                     "error": f"创建渲染任务失败: {response.status_code} - {response.text}",
#                     "video_url": None,
#                     "local_path": None
#                 }
            
#             # 3. 解析响应
#             render_data = response.json()
#             if isinstance(render_data, list) and len(render_data) > 0:
#                 render_data = render_data[0]
            
#             render_id = render_data.get('id')
#             if not render_id:
#                 return {
#                     "success": False,
#                     "error": "响应中未找到任务ID",
#                     "video_url": None,
#                     "local_path": None
#                 }
            
#             logger.info(f"✅ 渲染任务创建成功！任务ID: {render_id}")
            
#             # 4. 等待渲染完成
#             video_url = await self._wait_for_completion(render_id)
#             if not video_url:
#                 return {
#                     "success": False,
#                     "error": "渲染失败或超时",
#                     "video_url": None,
#                     "local_path": None
#                 }
            
#             # 5. 可选下载
#             local_path = None
#             if args.auto_download:
#                 download_path = self._get_download_path(args.download_path)
#                 local_path = await self._download_video(video_url, download_path, render_id)
            
#             return {
#                 "success": True,
#                 "error": None,
#                 "video_url": video_url,
#                 "local_path": local_path,
#                 "render_id": render_id,
#                 "metadata": {
#                     "video_type": args.video_type,
#                     "output_format": args.output_format,
#                     "output_size": f"{args.output_width}x{args.output_height}"
#                 }
#             }
            
#         except Exception as e:
#             logger.error(f"❌ 处理视频时发生错误: {e}")
#             return {
#                 "success": False,
#                 "error": str(e),
#                 "video_url": None,
#                 "local_path": None
#             }


# # === Tool Factory ===

# async def _aexecute_creatomate_video(config: Configuration, **kwargs) -> Dict[str, Any]:
#     """异步执行Creatomate视频创建"""
#     processor = CreatomateVideoProcessor(config)
#     args = CreatomateVideoInput(**kwargs)
#     return await processor.process_video(args)


# def _sync_execute_creatomate_video(config: Configuration, **kwargs) -> str:
#     """同步包装器 - 返回Agent友好的字符串结果"""
#     try:
#         result = asyncio.run(_aexecute_creatomate_video(config, **kwargs))
        
#         if result["success"]:
#             response_parts = [
#                 "🎬 Creatomate视频制作完成！",
#                 f"🔗 在线视频: {result['video_url']}"
#             ]
            
#             if result.get("local_path"):
#                 response_parts.append(f"💾 本地文件: {result['local_path']}")
            
#             if result.get("metadata"):
#                 metadata = result["metadata"]
#                 response_parts.append(f"📊 视频信息: {metadata['output_size']}, {metadata['output_format']}")
            
#             return "\n".join(response_parts)
#         else:
#             return f"❌ Creatomate视频制作失败: {result['error']}"
            
#     except Exception as e:
#         return f"❌ Creatomate执行错误: {str(e)}"


# def get_creatomate_video_tool(config: Configuration) -> Optional[StructuredTool]:
#     """创建Creatomate视频工具 - DeerFlow标准版本"""
    
#     # 检查API密钥配置
#     api_key = (
#         os.getenv("CREATOMATE_API_KEY") or 
#         getattr(config, 'creatomate_api_key', None)
#     )
    
#     if not api_key:
#         logger.warning("Creatomate视频工具已禁用: 未配置 CREATOMATE_API_KEY")
#         return None
    
#     return StructuredTool.from_function(
#         func=partial(_sync_execute_creatomate_video, config),
#         coroutine=partial(_aexecute_creatomate_video, config),
#         name="creatomate_video_creator",
#         description=(
#             "🎬 Creatomate专业视频制作工具 - 企业级云端视频渲染解决方案\n\n"
#             "**核心优势:**\n"
#             "• 🎯 Agent友好设计: 提供简单、模板、自定义三种使用模式\n"
#             "• ⚡ 云端渲染: 无需本地GPU，专业级视频处理能力\n"
#             "• 🔄 异步处理: 支持长时间渲染，实时进度监控\n"
#             "• 💾 自动下载: 渲染完成后自动保存到本地\n"
#             "• 🛡️ 健壮性: 完善的错误处理和重试机制\n\n"
#             "**使用模式:**\n"
#             "1. **简单模式 (video_type='simple')**:\n"
#             "   - 适合Agent快速创建视频\n"
#             "   - 只需提供背景视频/音频、标题、字幕\n"
#             "   - 自动处理布局和样式\n\n"
#             "2. **模板模式 (video_type='template')**:\n"
#             "   - 使用预设计的专业模板\n"
#             "   - 通过template_variables替换动态内容\n"
#             "   - 快速产出高质量视频\n\n"
#             "3. **自定义模式 (video_type='custom')**:\n"
#             "   - 完全自定义视频配置\n"
#             "   - 支持复杂多轨道、特效、动画\n"
#             "   - 适合高级用户和复杂需求\n\n"
#             "**适用场景:**\n"
#             "• 📱 短视频制作: 抖音、快手等平台内容\n"
#             "• 🎯 营销视频: 产品宣传、广告投放\n"
#             "• 📚 教育内容: 课程视频、知识分享\n"
#             "• 🏢 企业宣传: 公司介绍、活动记录\n"
#             "• 🤖 自动化内容: 批量个性化视频生成\n\n"
#             "配置API密钥: 设置环境变量 CREATOMATE_API_KEY"
#         ),
#         args_schema=CreatomateVideoInput,
#     )


# # === Independent Testing Support ===

# if __name__ == "__main__":
#     """独立测试支持 - 遵循DeerFlow法则#6"""
#     import logging
#     from src.config.configuration import Configuration
    
#     # 配置日志
#     logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
#     async def test_simple_mode():
#         """测试简单模式"""
#         print("🧪 测试简单模式...")
        
#         config = Configuration.from_env()
#         processor = CreatomateVideoProcessor(config)
        
#         args = CreatomateVideoInput(
#             video_type="simple",
#             background_video="https://cdn.creatomate.com/demo/drone.mp4",
#             title_text="DeerFlow测试视频",
#             subtitles=[
#                 {"text": "这是第一行字幕", "start": 0, "duration": 3},
#                 {"text": "这是第二行字幕", "start": 3, "duration": 3}
#             ],
#             auto_download=False  # 测试时不下载
#         )
        
#         result = await processor.process_video(args)
#         print(f"结果: {result}")
    
#     async def test_template_mode():
#         """测试模板模式"""
#         print("🧪 测试模板模式...")
        
#         config = Configuration.from_env()
#         processor = CreatomateVideoProcessor(config)
        
#         args = CreatomateVideoInput(
#             video_type="template",
#             template_id="a060b933-ee96-4e91-bbd7-d478de7b0a5e",
#             template_variables={
#                 'Text': 'DeerFlow模板测试',
#                 'Video': 'https://cdn.creatomate.com/demo/drone.mp4'
#             },
#             auto_download=False
#         )
        
#         result = await processor.process_video(args)
#         print(f"结果: {result}")
    
#     # 运行测试
#     asyncio.run(test_simple_mode())
#     # asyncio.run(test_template_mode())
