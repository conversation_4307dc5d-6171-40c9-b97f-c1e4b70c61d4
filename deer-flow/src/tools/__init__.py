# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
from functools import partial
from langchain_core.tools import StructuredTool

from src.config.configuration import Configuration
# from .crawl import crawl_tool
# from .python_repl import python_repl_tool
# from .search import (
#     duckduckgo_search_tool,
#     brave_search_tool,
#     arxiv_search_tool,
# )
# from .web import tavily_search_tool, LoggedTavilySearch
# from src.config import SELECTED_SEARCH_ENGINE, SearchEngine
from .audio.music_generation import get_suno_music_generation_tool
from .audio.text_to_speech import (
    get_text_to_speech_tool,
    get_text_to_voice_tool
)
from .audio.voice_clone import get_voice_clone_tool
from .audio.multi_speaker_tts import get_multi_speaker_tts_tool
# from .image import all_tools as image_tools
# === Video Tools (Temporarily Disabled) ===
# from .video import (
#     get_image_to_video_tool,
#     get_text_to_video_tool,
#     get_video_synthesis_tool,
# )
# from .video import video_tools 

# === Visual Tools ===
# For jmeng image generation
from .image.text_to_image import get_jmeng_image_generation_tool
# For flux image editor
from .image.flux_image_edit import get_flux_image_edit_tool
# For multi-image flux editor
from .image.multi_image_edit import get_multi_image_flux_edit_tool
# === Video Tools ===
from .video import (
    get_image_to_video_tool,
    get_text_to_video_tool,
    get_video_synthesis_tool,
    get_creatomate_video_tool_v2,
)
# === Expert Tools ===
from .experts import get_visual_expert_tool, get_audio_expert_tool, get_video_expert_tool

# === Understanding Tools ===
from .understanding import (
    get_multimodal_understanding_tool,
    get_image_understanding_tool,
    get_video_understanding_tool
)

# --- Tool Instantiation ---
# Create a single configuration instance to be used by all tools
config = Configuration.from_runnable_config()

# Instantiate the tools
jmeng_tool = get_jmeng_image_generation_tool(config=config)
flux_tool = get_flux_image_edit_tool(config=config)
multi_image_flux_tool = get_multi_image_flux_edit_tool(config=config)

# === Audio Tools ===
suno_tool = get_suno_music_generation_tool(config=config)
tts_tool = get_text_to_speech_tool(config=config)
voice_clone_tool = get_voice_clone_tool(config=config)
text_to_voice_tool = get_text_to_voice_tool(config=config)
multi_speaker_tts_tool = get_multi_speaker_tts_tool(config=config)


image_to_video_tool = get_image_to_video_tool(config=config)
text_to_video_tool = get_text_to_video_tool(config=config)
video_synthesis_tool = get_video_synthesis_tool(config=config) # This tool might not need config yet
creatomate_video_tool = get_creatomate_video_tool_v2(config=config)

# A list of all tools available for the Visual Agent
visual_tools = [jmeng_tool, flux_tool, multi_image_flux_tool]
# Filter out None values in case a tool failed to initialize
visual_tools = [tool for tool in visual_tools if tool is not None]

# A list of all tools available for the Video Agent
video_tools_list = [image_to_video_tool, text_to_video_tool, video_synthesis_tool, creatomate_video_tool]
video_tools = [tool for tool in video_tools_list if tool is not None]

# A list of all tools available for the Audio Agent
audio_tools = [suno_tool, tts_tool, voice_clone_tool, text_to_voice_tool, multi_speaker_tts_tool]
audio_tools = [tool for tool in audio_tools if tool is not None]

# === Understanding Tools ===
multimodal_understanding_tool = get_multimodal_understanding_tool(config=config)
image_understanding_tool = get_image_understanding_tool(config=config)
video_understanding_tool = get_video_understanding_tool(config=config)

understanding_tools_list = [multimodal_understanding_tool, image_understanding_tool, video_understanding_tool]
understanding_tools = [tool for tool in understanding_tools_list if tool is not None]

# A list of all expert tools
visual_expert_tool = get_visual_expert_tool(tools_list=visual_tools)
audio_expert_tool = get_audio_expert_tool(tools_list=audio_tools)
video_expert_tool = get_video_expert_tool(tools_list=video_tools)
expert_tools_list = [visual_expert_tool, audio_expert_tool, video_expert_tool]
expert_tools = [tool for tool in expert_tools_list if tool is not None]


# Combine all tools from different categories
# Note: This is a placeholder for a more sophisticated tool registration system.
# For now, we manually list the tools for different agents.
__all__ = [
    "audio_tools",
    "video_tools",
    "visual_tools",
    "understanding_tools",  # <-- Add understanding tools
    "expert_tools", # <-- Add the new expert tools list
    "jmeng_tool",
    "flux_tool",
    "multi_image_flux_tool",
    "suno_tool",
    "tts_tool",
    "voice_clone_tool",
    "text_to_voice_tool",
    "multi_speaker_tts_tool",
    "image_to_video_tool",
    "text_to_video_tool",
    "video_synthesis_tool",
    "creatomate_video_tool",
    "multimodal_understanding_tool",  # <-- Export understanding tools
    "image_understanding_tool",
    "video_understanding_tool",
    "visual_expert_tool", # <-- Export the new tool instance
    "audio_expert_tool",
    "video_expert_tool",
]

# It might be beneficial to also create a TOOL_MAP as suggested in your docs
# for easier lookup, e.g.:
_ALL_TOOLS_CANDIDATES = [
    # crawl_tool,
    # web_search_tool,
    # python_repl_tool,
    *audio_tools,
    # *image_tools,
    *video_tools,
    *visual_tools,
    *understanding_tools,  # <-- Add understanding tools to full list
    *expert_tools, # <-- Also add to the full tool list
]
ALL_TOOLS_LIST = [tool for tool in _ALL_TOOLS_CANDIDATES if tool is not None]
TOOL_MAP = {tool.name: tool for tool in ALL_TOOLS_LIST if hasattr(tool, 'name')}
# This part is commented out as it deviates from the current file structure but is a recommendation.
