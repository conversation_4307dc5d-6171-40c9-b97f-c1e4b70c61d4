#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实AI JSON生成测试

使用我们构建的JSON Agent真实调用LLM来生成Creatomate JSON配置
"""

import sys
import json
import asyncio
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_real_ai_json_generation():
    """测试真实的AI JSON生成"""
    print("🧠 真实AI JSON生成测试")
    print("="*60)
    
    try:
        # 导入我们的模块
        from src.config.configuration import Configuration
        from src.tools.video.creatomate_json_agent import convert_to_creatomate_json
        
        print("✅ 模块导入成功")
        
        # 创建配置
        config = Configuration()
        print("✅ 配置创建成功")
        
        # 测试输入：您提供的真实场景
        test_input = {
            "video_config": {
                "width": 1920,
                "height": 1080,
                "output_format": "mp4"
            },
            "scenes": [
                {
                    "duration": 3,
                    "background_video": "https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/30399726600-1-192.mp4",
                    "audio": "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/deerflow_assets/赵本山小品之相当有钱_爱给网_aigei_com.mp3", 
                    "subtitle": "现在有钱"
                },
                {
                    "duration": 4,
                    "background_video": "继续猩猩",
                    "overlay_video": {"url": "792685ef-1ad9-4ed4-80f1-f66d0055e74f", "position": "right"},
                    "subtitle": "瞅着穿的相当有钱"
                }
            ]
        }
        
        print("\n📋 测试输入:")
        print(json.dumps(test_input, ensure_ascii=False, indent=2))
        
        print("\n🚀 调用AI JSON Agent...")
        print("使用reasoning级别的LLM模型")
        print("应用完整的JSON规则和工程保障")
        
        # 真实调用AI生成JSON
        result = await convert_to_creatomate_json(config, test_input)
        
        print("\n🎉 AI生成成功！")
        print("="*60)
        print("📋 AI生成的完整Creatomate JSON:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 分析生成结果
        analyze_ai_result(result)
        
        return result
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {str(e)}")
        print("请确保在deer-flow目录下运行此脚本")
        return None
        
    except Exception as e:
        print(f"❌ AI调用失败: {str(e)}")
        logger.error(f"详细错误: {str(e)}", exc_info=True)
        return None

def analyze_ai_result(result):
    """分析AI生成的结果"""
    print("\n🔍 AI生成结果分析")
    print("="*40)
    
    if not result or "source" not in result:
        print("❌ 结果无效或格式错误")
        return
    
    source = result["source"]
    elements = source.get("elements", [])
    
    print(f"📊 基础信息:")
    print(f"• 输出格式: {source.get('output_format', '未指定')}")
    print(f"• 分辨率: {source.get('width', '?')}x{source.get('height', '?')}")
    print(f"• 总时长: {source.get('duration', '未指定')}秒")
    print(f"• 元素数量: {len(elements)}个")
    
    print(f"\n🎬 元素分析:")
    element_types = {}
    for element in elements:
        element_type = element.get("type", "未知")
        element_types.setdefault(element_type, []).append(element)
    
    for element_type, type_elements in element_types.items():
        print(f"\n{element_type.upper()}元素 ({len(type_elements)}个):")
        for element in type_elements:
            element_id = element.get("id", "无ID")
            track = element.get("track", "?")
            time = element.get("time", "?")
            
            if element_type == "text":
                text = element.get("text", "")
                font_family = element.get("font_family", "未指定")
                has_background = "background_color" in element
                print(f"  • \"{text}\" [ID:{element_id}] track:{track}, time:{time}s, 字体:{font_family}, 背景:{'有' if has_background else '无'}")
                
            elif element_type == "video":
                width = element.get("width", "?")
                height = element.get("height", "?")
                volume = element.get("volume", "?")
                print(f"  • [ID:{element_id}] track:{track}, time:{time}s, 尺寸:{width}x{height}, 音量:{volume}")
                
            elif element_type == "audio":
                volume = element.get("volume", "?")
                fade_in = element.get("fade_in", "无")
                fade_out = element.get("fade_out", "无")
                print(f"  • [ID:{element_id}] track:{track}, time:{time}s, 音量:{volume}, 淡入:{fade_in}, 淡出:{fade_out}")

def validate_ai_quality(result):
    """验证AI生成质量"""
    print("\n✅ AI生成质量验证")
    print("="*40)
    
    if not result or "source" not in result:
        print("❌ 基础结构验证失败")
        return False
    
    source = result["source"]
    elements = source.get("elements", [])
    
    quality_checks = []
    
    # 检查基础字段
    required_fields = ["output_format", "width", "height", "elements"]
    for field in required_fields:
        if field in source:
            quality_checks.append(f"✅ {field}字段存在")
        else:
            quality_checks.append(f"❌ {field}字段缺失")
    
    # 检查元素质量
    has_descriptive_ids = all("_" in element.get("id", "") for element in elements)
    quality_checks.append(f"{'✅' if has_descriptive_ids else '❌'} ID命名描述性")
    
    has_chinese_font = any(element.get("font_family") == "Microsoft YaHei" for element in elements if element.get("type") == "text")
    quality_checks.append(f"{'✅' if has_chinese_font else '❌'} 中文字体支持")
    
    has_text_background = any("background_color" in element for element in elements if element.get("type") == "text")
    quality_checks.append(f"{'✅' if has_text_background else '❌'} 文字背景设置")
    
    has_audio_config = any(element.get("volume") and element.get("volume") != "0%" for element in elements if element.get("type") == "audio")
    quality_checks.append(f"{'✅' if has_audio_config else '❌'} 音频配置完整")
    
    # 显示结果
    for check in quality_checks:
        print(f"  {check}")
    
    success_count = sum(1 for check in quality_checks if check.startswith("✅"))
    total_count = len(quality_checks)
    
    print(f"\n📊 质量评分: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    return success_count / total_count >= 0.8

async def test_multiple_scenarios():
    """测试多种场景"""
    print("\n🎭 多场景测试")
    print("="*60)
    
    scenarios = [
        {
            "name": "鬼畜视频场景",
            "input": {
                "scenes": [
                    {
                        "duration": 3,
                        "background_video": "猩猩跳舞视频",
                        "audio": "宋丹丹音频", 
                        "subtitle": "现在有钱"
                    },
                    {
                        "duration": 4,
                        "overlay_video": {"url": "哪吒视频", "position": "right"},
                        "subtitle": "瞅着穿的相当有钱"
                    }
                ]
            }
        },
        {
            "name": "自然语言场景",
            "input": "制作一个7秒的视频，背景是猩猩跳舞，配上搞笑音频，3秒后右边出现哪吒，显示相关字幕"
        },
        {
            "name": "产品宣传场景",
            "input": {
                "scenes": [
                    {
                        "duration": 5,
                        "background_video": "产品展示视频",
                        "title": "新品发布",
                        "subtitle": "革命性的AI工具"
                    }
                ]
            }
        }
    ]
    
    try:
        from src.config.configuration import Configuration
        from src.tools.video.creatomate_json_agent import convert_to_creatomate_json
        
        config = Configuration()
        results = []
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n📋 场景{i}: {scenario['name']}")
            print("-" * 40)
            
            try:
                result = await convert_to_creatomate_json(config, scenario['input'])
                
                if result:
                    print(f"✅ 生成成功，包含{len(result.get('source', {}).get('elements', []))}个元素")
                    results.append((scenario['name'], True, result))
                else:
                    print("❌ 生成失败")
                    results.append((scenario['name'], False, None))
                    
            except Exception as e:
                print(f"❌ 生成出错: {str(e)}")
                results.append((scenario['name'], False, None))
        
        # 汇总结果
        print(f"\n📊 多场景测试结果:")
        success_count = 0
        for name, success, result in results:
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {name}: {status}")
            if success:
                success_count += 1
        
        print(f"\n🎯 总体成功率: {success_count}/{len(scenarios)} ({success_count/len(scenarios)*100:.1f}%)")
        
        return results
        
    except Exception as e:
        print(f"❌ 多场景测试失败: {str(e)}")
        return []

async def main():
    """主测试函数"""
    print("🧠 真实AI JSON生成器测试")
    print("="*60)
    print("使用DeerFlow的reasoning级别LLM模型")
    print("应用完整的JSON Agent规则和工程保障")
    print("="*60)
    
    # 测试1: 基础场景
    result = await test_real_ai_json_generation()
    
    if result:
        # 验证质量
        is_high_quality = validate_ai_quality(result)
        
        print(f"\n🏆 基础测试结果:")
        print(f"• AI调用: ✅ 成功")
        print(f"• JSON生成: ✅ 完整")
        print(f"• 质量评级: {'🌟 优秀' if is_high_quality else '⚠️ 需改进'}")
        
        # 测试2: 多场景测试
        await test_multiple_scenarios()
        
    else:
        print("\n❌ 基础测试失败，跳过后续测试")
        print("\n🔧 可能的解决方案:")
        print("1. 检查LLM配置是否正确")
        print("2. 确认API密钥和网络连接")
        print("3. 检查deer-flow项目路径")
    
    print("\n" + "="*60)
    print("🎉 真实AI测试完成！")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
