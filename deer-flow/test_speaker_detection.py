#!/usr/bin/env python3
"""
测试说话人识别问题
"""

import logging
import sys
import os
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_volcengine_raw_response():
    """测试火山引擎API的原始返回数据"""
    logger.info("🔍 测试火山引擎API原始返回数据...")
    
    # 测试视频URL
    video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Km5kzHEim_GAOQING.mp4"
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入必要的模块
        from tools.audio.video_subtitle_extraction import (
            _process_media_input, _submit_subtitle_task, _query_subtitle_result
        )
        from config.configuration import Configuration
        
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 获取API密钥
        api_key = os.getenv("VOLCENGINE_SUBTITLE_KEY")
        appid = os.getenv("VOLCENGINE_SUBTITLE_APPID")
        
        if not api_key or not appid:
            logger.error("❌ 缺少火山引擎API配置")
            return False
        
        logger.info(f"🔑 API Key: {api_key[:10]}...")
        logger.info(f"🆔 App ID: {appid}")
        
        # 处理媒体输入
        logger.info("📹 处理媒体输入...")
        media_result = _process_media_input(video_url)
        
        if not media_result["success"]:
            logger.error(f"❌ 媒体处理失败: {media_result['error']}")
            return False
        
        audio_url = media_result["audio_url"]
        logger.info(f"🎵 音频URL: {audio_url}")

        # 从file://路径中提取实际路径
        if audio_url.startswith("file://"):
            audio_path = audio_url[7:]  # 移除"file://"前缀
        else:
            audio_path = audio_url
        
        # 创建测试参数
        class TestArgs:
            def __init__(self):
                self.language = "zh-CN"
                self.auto_speaker_identification = True
                self.speaker_mapping = None
        
        test_args = TestArgs()
        
        # 提交字幕任务
        logger.info("📤 提交字幕任务...")
        submit_result = _submit_subtitle_task(api_key, appid, audio_url, test_args)
        
        if not submit_result["success"]:
            logger.error(f"❌ 任务提交失败: {submit_result['error']}")
            return False
        
        task_id = submit_result["task_id"]
        logger.info(f"✅ 任务提交成功: {task_id}")
        
        # 查询结果
        logger.info("📥 查询字幕结果...")
        query_result = _query_subtitle_result(api_key, appid, task_id)
        
        if not query_result["success"]:
            logger.error(f"❌ 查询结果失败: {query_result['error']}")
            return False
        
        raw_data = query_result["data"]
        logger.info("✅ 查询结果成功")
        
        # 保存原始数据
        with open("volcengine_raw_response.json", 'w', encoding='utf-8') as f:
            json.dump(raw_data, f, ensure_ascii=False, indent=2)
        logger.info("💾 原始数据已保存到: volcengine_raw_response.json")
        
        # 分析原始数据结构
        logger.info("\n" + "="*80)
        logger.info("📊 分析火山引擎API原始数据结构")
        logger.info("="*80)
        
        # 检查顶层结构
        logger.info("🔍 顶层结构:")
        for key in raw_data.keys():
            logger.info(f"   {key}: {type(raw_data[key])}")
        
        # 检查data字段
        if "data" in raw_data:
            data = raw_data["data"]
            logger.info("🔍 data字段结构:")
            for key in data.keys():
                logger.info(f"   {key}: {type(data[key])}")
            
            # 检查utterances
            if "utterances" in data:
                utterances = data["utterances"]
                logger.info(f"🔍 utterances: {len(utterances)} 个")
                
                # 分析前几个utterance的结构
                for i, utterance in enumerate(utterances[:5]):
                    logger.info(f"   utterance {i+1}:")
                    logger.info(f"      keys: {list(utterance.keys())}")
                    
                    # 检查attribute字段
                    if "attribute" in utterance:
                        attribute = utterance["attribute"]
                        logger.info(f"      attribute: {attribute}")
                        
                        if "speaker" in attribute:
                            speaker = attribute["speaker"]
                            logger.info(f"      🎤 speaker: {speaker}")
                        else:
                            logger.info("      ❌ 没有speaker字段")
                    else:
                        logger.info("      ❌ 没有attribute字段")
                    
                    # 检查words字段
                    if "words" in utterance:
                        words = utterance["words"]
                        logger.info(f"      words: {len(words)} 个")
                        
                        # 检查前几个word的speaker信息
                        for j, word in enumerate(words[:3]):
                            if "attribute" in word:
                                word_attr = word["attribute"]
                                if "speaker" in word_attr:
                                    logger.info(f"         word {j+1} speaker: {word_attr['speaker']}")
                                else:
                                    logger.info(f"         word {j+1}: 没有speaker")
                            else:
                                logger.info(f"         word {j+1}: 没有attribute")
                    
                    logger.info("")
        
        # 统计说话人信息
        logger.info("\n" + "="*80)
        logger.info("📈 说话人统计分析")
        logger.info("="*80)
        
        speaker_ids = set()
        utterance_speakers = []
        word_speakers = []
        
        if "data" in raw_data and "utterances" in raw_data["data"]:
            utterances = raw_data["data"]["utterances"]
            
            for utterance in utterances:
                # 检查utterance级别的speaker
                utterance_speaker = None
                if "attribute" in utterance and "speaker" in utterance["attribute"]:
                    utterance_speaker = utterance["attribute"]["speaker"]
                    speaker_ids.add(utterance_speaker)
                
                utterance_speakers.append(utterance_speaker)
                
                # 检查words级别的speaker
                if "words" in utterance:
                    for word in utterance["words"]:
                        if "attribute" in word and "speaker" in word["attribute"]:
                            word_speaker = word["attribute"]["speaker"]
                            speaker_ids.add(word_speaker)
                            word_speakers.append(word_speaker)
        
        logger.info(f"🎤 发现的说话人ID: {speaker_ids}")
        logger.info(f"📊 utterance级别说话人: {len([s for s in utterance_speakers if s is not None])} / {len(utterance_speakers)}")
        logger.info(f"📊 word级别说话人: {len([s for s in word_speakers if s is not None])} / {len(word_speakers)}")
        
        # 分析为什么没有检测到多个说话人
        if len(speaker_ids) <= 1:
            logger.warning("⚠️ 说话人识别问题分析:")
            logger.warning("   1. 可能这个视频确实只有一个说话人")
            logger.warning("   2. 可能音频质量不够好，无法区分说话人")
            logger.warning("   3. 可能需要调整火山引擎API参数")
            logger.warning("   4. 可能需要更长的音频才能有效识别")
            
            # 检查音频时长
            if "data" in raw_data:
                data = raw_data["data"]
                if "duration" in data:
                    duration = data["duration"]
                    logger.info(f"   音频时长: {duration}ms ({duration/1000:.1f}秒)")
                    
                    if duration < 30000:  # 少于30秒
                        logger.warning("   ⚠️ 音频时长较短，可能影响说话人识别效果")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_speaker_video():
    """测试一个明确有多个说话人的视频"""
    logger.info("🎭 建议测试多说话人视频...")

    logger.info("💡 为了更好地测试说话人识别功能，建议使用:")
    logger.info("   1. 明确有多个人对话的视频")
    logger.info("   2. 声音差异较大的说话人")
    logger.info("   3. 较长的对话片段（每人至少说几句话）")
    logger.info("   4. 音质清晰的音频")
    logger.info("")
    logger.info("🎬 例如:")
    logger.info("   • 赵本山小品（有明显的男女声对话）")
    logger.info("   • 访谈节目（主持人+嘉宾）")
    logger.info("   • 电影对话片段")
    logger.info("   • 播客节目")

    return True  # 这个函数总是成功

def main():
    """主函数"""
    logger.info("🎯 开始说话人识别问题测试...")
    logger.info("目标: 分析为什么只检测到一个说话人")
    
    tests = [
        ("火山引擎原始数据分析", test_volcengine_raw_response),
        ("多说话人视频建议", test_multi_speaker_video)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*80}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*80}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 完成")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*80}")
    logger.info("🎯 说话人识别问题分析总结")
    logger.info(f"{'='*80}")
    
    logger.info("🔍 **可能的原因:**")
    logger.info("   1. 测试视频确实只有一个说话人")
    logger.info("   2. 音频质量不足以区分不同说话人")
    logger.info("   3. 说话人声音特征相似")
    logger.info("   4. 火山引擎API参数需要调整")
    logger.info("")
    logger.info("💡 **解决方案:**")
    logger.info("   1. 使用明确有多个说话人的测试视频")
    logger.info("   2. 检查音频质量和清晰度")
    logger.info("   3. 尝试调整API参数")
    logger.info("   4. 如果确实只有一个说话人，这是正常行为")
    logger.info("")
    logger.info("📁 **生成的文件:**")
    logger.info("   • volcengine_raw_response.json - 火山引擎API原始返回数据")

if __name__ == "__main__":
    main()
