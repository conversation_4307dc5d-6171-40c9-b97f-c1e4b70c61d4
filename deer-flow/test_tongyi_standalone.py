#!/usr/bin/env python3
"""
独立的通义听悟适配器测试
不依赖其他模块
"""

import logging
import json
import time
import requests

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleTongyiAdapter:
    """简化的通义听悟适配器"""
    
    def __init__(self, access_key_id: str, access_key_secret: str, app_key: str):
        """初始化适配器"""
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.app_key = app_key
        
        # 导入阿里云SDK
        from aliyunsdkcore.client import AcsClient
        from aliyunsdkcore.request import CommonRequest
        from aliyunsdkcore.auth.credentials import AccessKeyCredential
        
        credentials = AccessKeyCredential(access_key_id, access_key_secret)
        self.client = AcsClient(region_id='cn-beijing', credential=credentials)
        self.CommonRequest = CommonRequest
    
    def convert_tongyi_to_volcengine(self, tongyi_data: dict) -> dict:
        """将通义听悟格式转换为火山引擎格式"""
        logger.info("🔄 转换数据格式...")
        
        try:
            # 提取段落数据
            if 'Transcription' not in tongyi_data or 'Paragraphs' not in tongyi_data['Transcription']:
                raise ValueError("Invalid data format")
            
            paragraphs = tongyi_data['Transcription']['Paragraphs']
            utterances = []
            
            for paragraph in paragraphs:
                speaker_id = paragraph.get('SpeakerId', '1')
                words = paragraph.get('Words', [])
                
                if not words:
                    continue
                
                # 合并词语为完整文本
                text_parts = []
                start_time = None
                end_time = None
                
                for word in words:
                    text_parts.append(word.get('Text', ''))
                    
                    # 记录时间范围
                    word_start = word.get('Start', 0)
                    word_end = word.get('End', 0)
                    
                    if start_time is None or word_start < start_time:
                        start_time = word_start
                    if end_time is None or word_end > end_time:
                        end_time = word_end
                
                # 构建utterance
                utterance = {
                    "start_time": start_time or 0,
                    "end_time": end_time or 0,
                    "text": ''.join(text_parts),
                    "attribute": {
                        "speaker": speaker_id
                    }
                }
                
                utterances.append(utterance)
            
            # 构建火山引擎格式的返回数据
            volcengine_format = {
                "utterances": utterances
            }
            
            logger.info(f"✅ 格式转换完成: {len(utterances)} 个utterances")
            return volcengine_format
            
        except Exception as e:
            logger.error(f"❌ 格式转换失败: {e}")
            raise

def test_format_conversion():
    """测试格式转换功能"""
    logger.info("🧪 测试格式转换功能...")
    
    try:
        # 读取之前保存的通义听悟原始数据
        with open('tongyi_transcription_result.json', 'r', encoding='utf-8') as f:
            tongyi_data = json.load(f)
        
        logger.info("✅ 读取通义听悟原始数据成功")
        
        # 创建适配器
        adapter = SimpleTongyiAdapter(
            "LTAI5tBTpMAPNJ3Z6jpZyTSo",
            "******************************", 
            "wbp1hepOKEWDiQEC"
        )
        
        # 转换格式
        volcengine_format = adapter.convert_tongyi_to_volcengine(tongyi_data)
        
        # 保存转换结果
        with open('tongyi_to_volcengine_converted.json', 'w', encoding='utf-8') as f:
            json.dump(volcengine_format, f, ensure_ascii=False, indent=2)
        
        logger.info("💾 转换结果已保存到: tongyi_to_volcengine_converted.json")
        
        # 分析转换结果
        analyze_converted_data(volcengine_format)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 格式转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_converted_data(volcengine_data):
    """分析转换后的数据"""
    logger.info("\n" + "="*80)
    logger.info("📊 转换结果分析")
    logger.info("="*80)
    
    try:
        utterances = volcengine_data.get("utterances", [])
        logger.info(f"📝 总utterances数量: {len(utterances)}")
        
        # 统计说话人
        speaker_stats = {}
        for utterance in utterances:
            speaker_id = utterance.get("attribute", {}).get("speaker", "unknown")
            text = utterance.get("text", "")
            start_time = utterance.get("start_time", 0)
            end_time = utterance.get("end_time", 0)
            duration = (end_time - start_time) / 1000.0  # 转换为秒
            
            if speaker_id not in speaker_stats:
                speaker_stats[speaker_id] = {
                    "count": 0,
                    "total_chars": 0,
                    "total_duration": 0,
                    "examples": []
                }
            
            speaker_stats[speaker_id]["count"] += 1
            speaker_stats[speaker_id]["total_chars"] += len(text)
            speaker_stats[speaker_id]["total_duration"] += duration
            
            if len(speaker_stats[speaker_id]["examples"]) < 3:
                speaker_stats[speaker_id]["examples"].append({
                    "text": text,
                    "start": start_time,
                    "end": end_time
                })
        
        logger.info(f"👥 说话人数量: {len(speaker_stats)}")
        
        # 详细统计
        for speaker_id, stats in speaker_stats.items():
            percentage = (stats["count"] / len(utterances)) * 100
            avg_chars = stats["total_chars"] / stats["count"] if stats["count"] > 0 else 0
            avg_duration = stats["total_duration"] / stats["count"] if stats["count"] > 0 else 0
            
            logger.info(f"\n🎤 说话人 {speaker_id}:")
            logger.info(f"   utterances数量: {stats['count']}")
            logger.info(f"   占比: {percentage:.1f}%")
            logger.info(f"   平均字数: {avg_chars:.1f}")
            logger.info(f"   平均时长: {avg_duration:.1f}秒")
            logger.info(f"   总时长: {stats['total_duration']:.1f}秒")
            logger.info(f"   示例:")
            for i, example in enumerate(stats["examples"]):
                start_sec = example["start"] / 1000.0
                end_sec = example["end"] / 1000.0
                logger.info(f"      {i+1}. [{start_sec:.1f}s-{end_sec:.1f}s] {example['text'][:50]}...")
        
        # 检查数据格式兼容性
        logger.info(f"\n🔍 火山引擎格式兼容性检查:")
        
        sample_utterance = utterances[0] if utterances else {}
        required_fields = ["start_time", "end_time", "text", "attribute"]
        
        all_fields_present = True
        for field in required_fields:
            if field in sample_utterance:
                logger.info(f"   ✅ {field}: {type(sample_utterance[field])}")
            else:
                logger.warning(f"   ❌ 缺少字段: {field}")
                all_fields_present = False
        
        # 检查说话人字段
        if "attribute" in sample_utterance and "speaker" in sample_utterance["attribute"]:
            logger.info(f"   ✅ speaker字段: {sample_utterance['attribute']['speaker']}")
        else:
            logger.warning(f"   ❌ 缺少speaker字段")
            all_fields_present = False
        
        # 生成对比报告
        comparison_report = {
            "原始格式": "通义听悟 Paragraphs + Words",
            "转换格式": "火山引擎 utterances",
            "说话人数量": len(speaker_stats),
            "utterances数量": len(utterances),
            "格式兼容性": "完全兼容" if all_fields_present else "需要调整",
            "说话人分布": {}
        }
        
        for speaker_id, stats in speaker_stats.items():
            comparison_report["说话人分布"][f"说话人{speaker_id}"] = {
                "utterances": stats["count"],
                "占比": f"{(stats['count'] / len(utterances)) * 100:.1f}%",
                "总时长": f"{stats['total_duration']:.1f}秒"
            }
        
        # 保存对比报告
        with open('format_conversion_report.json', 'w', encoding='utf-8') as f:
            json.dump(comparison_report, f, ensure_ascii=False, indent=2)
        
        logger.info("💾 对比报告已保存到: format_conversion_report.json")
        
        logger.info(f"\n🎯 转换结论:")
        if len(speaker_stats) > 1 and all_fields_present:
            logger.info("🎉 格式转换完全成功！")
            logger.info("✅ 保留了所有说话人信息")
            logger.info("✅ 数据格式完全兼容火山引擎")
            logger.info("✅ 可以直接替换到现有工具中")
        elif len(speaker_stats) > 1:
            logger.info("✅ 说话人信息转换成功")
            logger.warning("⚠️ 数据格式需要微调")
        else:
            logger.warning("⚠️ 说话人信息可能有问题")
            
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")

def main():
    """主函数"""
    logger.info("🎯 通义听悟格式转换测试")
    logger.info("="*60)
    
    success = test_format_conversion()
    
    logger.info(f"\n{'='*80}")
    logger.info("🎯 测试总结")
    logger.info(f"{'='*80}")
    
    if success:
        logger.info("🎉 格式转换测试成功！")
        logger.info("")
        logger.info("📁 生成的文件:")
        logger.info("   • tongyi_to_volcengine_converted.json - 转换后的火山引擎格式")
        logger.info("   • format_conversion_report.json - 格式转换对比报告")
        logger.info("")
        logger.info("🚀 下一步:")
        logger.info("   1. 检查转换结果的准确性")
        logger.info("   2. 集成到现有视频字幕提取工具")
        logger.info("   3. 添加通义听悟作为备选API")
        logger.info("")
        logger.info("💡 集成建议:")
        logger.info("   • 保留火山引擎作为主要API")
        logger.info("   • 当火山引擎说话人识别失败时，自动切换到通义听悟")
        logger.info("   • 或者直接将通义听悟设为默认API")
    else:
        logger.error("❌ 格式转换测试失败")
        logger.info("🔧 需要检查数据格式和转换逻辑")

if __name__ == "__main__":
    main()
