#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接测试JSON Agent

绕过复杂依赖，直接测试我们的JSON Agent核心功能
"""

import sys
import json
import asyncio
import logging
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 简化导入，避免复杂依赖
try:
    from src.llms.llm import get_llm_by_type
    from src.config.agents import LLMType
    LLM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ LLM模块导入失败: {e}")
    LLM_AVAILABLE = False

class SimpleCreatomateJSONAgent:
    """简化版的Creatomate JSON Agent"""
    
    def __init__(self):
        if LLM_AVAILABLE:
            try:
                self.llm = get_llm_by_type("reasoning")
                self.llm_ready = True
                print("✅ LLM初始化成功 - 使用reasoning级别模型")
            except Exception as e:
                print(f"❌ LLM初始化失败: {e}")
                self.llm_ready = False
        else:
            self.llm_ready = False
        
        # 详细的JSON规则（从我们的agent复制）
        self.json_rules = """
## Creatomate JSON语法规则 - 完整专家指南

### 🏗️ 基础结构模板（必须严格遵循）
```json
{
  "source": {
    "output_format": "mp4",
    "width": 1920,
    "height": 1080,
    "duration": 总时长(秒),
    "elements": [
      {
        "id": "unique_element_id",
        "type": "video|audio|text|image|shape",
        "track": 轨道编号,
        "time": 开始时间(秒),
        "duration": 持续时间(秒)
      }
    ]
  }
}
```

### 🎬 轨道系统规则
- track 1: 背景视频/图片（最底层）
- track 2: 主要覆盖视频/人物（中间层）
- track 4: 主音频/背景音乐
- track 5+: 文本/字幕层（最上层）

### 🧩 元素类型完整规范

#### 视频元素
```json
{
  "id": "video_element_1",
  "type": "video",
  "track": 1,
  "time": 0,
  "duration": 5,
  "source": "URL",
  "volume": "0%",
  "x": "50%", "y": "50%",
  "width": "100%", "height": "100%"
}
```

#### 音频元素
```json
{
  "id": "audio_element_1",
  "type": "audio",
  "track": 4,
  "time": 0,
  "source": "音频URL",
  "volume": "85%"
}
```

#### 文本元素
```json
{
  "id": "text_element_1", 
  "type": "text",
  "track": 5,
  "time": 0,
  "duration": 3,
  "text": "显示文字",
  "font_family": "Microsoft YaHei",
  "font_size": 40,
  "fill_color": "#ffffff",
  "stroke_color": "#333333",
  "x": "50%", "y": "85%",
  "background_color": "rgba(0,0,0,0.6)"
}
```
"""

    async def convert_to_creatomate_json(self, loose_input):
        """将宽松输入转换为标准Creatomate JSON"""
        
        if not self.llm_ready:
            print("❌ LLM未就绪，无法进行转换")
            return None
        
        # 构建专家级prompt
        input_str = json.dumps(loose_input, ensure_ascii=False, indent=2) if isinstance(loose_input, dict) else str(loose_input)
        
        prompt = f"""
你是世界顶级的Creatomate视频制作JSON专家。请将用户需求转换为完美的Creatomate JSON配置。

## 专业知识库：
{self.json_rules}

## 用户输入：
```
{input_str}
```

## 转换要求：
1. 严格按照JSON语法规则生成标准配置
2. 智能推断缺失信息，提供合理默认值
3. 优化时间轴逻辑，确保元素时间安排合理
4. 使用科学的轨道分配策略
5. 应用专业的样式配置

## 输出要求：
只输出纯JSON，不要任何说明文字。JSON必须：
- 包含完整的source结构
- 所有元素都有正确的track、time、duration
- 使用描述性的ID命名
- 文字使用Microsoft YaHei字体和半透明背景
- 音量配置合理

现在开始转换：
"""
        
        try:
            print("🚀 开始调用LLM生成JSON...")
            response = await self.llm.ainvoke(prompt)
            
            print("✅ LLM响应成功")
            
            # 解析响应
            content = response.content.strip()
            
            # 清理markdown标记
            if content.startswith("```json"):
                content = content[7:]
            if content.startswith("```"):
                content = content[3:]
            if content.endswith("```"):
                content = content[:-3]
            
            content = content.strip()
            
            print("📋 LLM原始响应:")
            print(content)
            
            # 解析JSON
            json_config = json.loads(content)
            
            print("✅ JSON解析成功")
            
            # 应用工程保障
            final_config = self._apply_safeguards(json_config)
            
            return final_config
            
        except Exception as e:
            print(f"❌ 转换过程出错: {str(e)}")
            logger.error(f"详细错误: {str(e)}", exc_info=True)
            return None
    
    def _apply_safeguards(self, json_config):
        """应用基础工程保障"""
        if "source" not in json_config:
            print("⚠️ 缺少source字段，尝试修复")
            if "output_format" in json_config:
                # 整个config就是source
                json_config = {"source": json_config}
        
        source = json_config["source"]
        
        # 基础配置
        source.setdefault("output_format", "mp4")
        source.setdefault("width", 1920)
        source.setdefault("height", 1080)
        
        if "elements" in source:
            # 为每个元素添加ID
            for i, element in enumerate(source["elements"]):
                if "id" not in element:
                    element_type = element.get("type", "element")
                    element["id"] = f"{element_type}_{i+1}"
        
        print("✅ 工程保障应用完成")
        return json_config

async def test_real_llm_generation():
    """测试真实的LLM生成"""
    print("🧠 真实LLM JSON生成测试")
    print("="*50)
    
    agent = SimpleCreatomateJSONAgent()
    
    if not agent.llm_ready:
        print("❌ LLM未就绪，无法进行测试")
        return None
    
    # 测试输入
    test_input = {
        "scenes": [
            {
                "duration": 3,
                "background_video": "https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/30399726600-1-192.mp4",
                "audio": "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/deerflow_assets/赵本山小品之相当有钱_爱给网_aigei_com.mp3", 
                "subtitle": "现在有钱"
            },
            {
                "duration": 4,
                "background_video": "继续猩猩",
                "overlay_video": {"url": "792685ef-1ad9-4ed4-80f1-f66d0055e74f", "position": "right"},
                "subtitle": "瞅着穿的相当有钱"
            }
        ]
    }
    
    print("📝 测试输入:")
    print(json.dumps(test_input, ensure_ascii=False, indent=2))
    
    # 调用AI
    result = await agent.convert_to_creatomate_json(test_input)
    
    if result:
        print("\n🎉 AI生成成功！")
        print("="*50)
        print("📋 AI生成的完整JSON:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 分析结果
        analyze_result(result)
        
        return result
    else:
        print("❌ AI生成失败")
        return None

def analyze_result(result):
    """分析AI生成结果"""
    print("\n🔍 AI生成结果分析")
    print("="*30)
    
    source = result.get("source", {})
    elements = source.get("elements", [])
    
    print(f"📊 基础信息:")
    print(f"• 元素数量: {len(elements)}个")
    print(f"• 总时长: {source.get('duration', '未指定')}秒")
    
    print(f"\n🎬 元素详情:")
    for i, element in enumerate(elements):
        element_type = element.get("type", "未知")
        element_id = element.get("id", f"元素{i+1}")
        track = element.get("track", "?")
        time = element.get("time", "?")
        
        if element_type == "text":
            text = element.get("text", "")
            font_family = element.get("font_family", "未指定")
            has_bg = "background_color" in element
            print(f"  {i+1}. 文字: \"{text}\" [track:{track}, time:{time}s, 字体:{font_family}, 背景:{'✅' if has_bg else '❌'}]")
        elif element_type == "video":
            volume = element.get("volume", "?")
            width = element.get("width", "?")
            print(f"  {i+1}. 视频: [ID:{element_id}, track:{track}, time:{time}s, 音量:{volume}, 宽度:{width}]")
        elif element_type == "audio":
            volume = element.get("volume", "?")
            print(f"  {i+1}. 音频: [ID:{element_id}, track:{track}, time:{time}s, 音量:{volume}]")
        else:
            print(f"  {i+1}. {element_type}: [ID:{element_id}, track:{track}, time:{time}s]")

async def test_simple_prompt():
    """测试简单提示"""
    print("\n🧪 简单提示测试")
    print("="*30)
    
    agent = SimpleCreatomateJSONAgent()
    
    if not agent.llm_ready:
        print("❌ 跳过：LLM未就绪")
        return
    
    simple_input = "制作一个5秒的视频，显示'Hello World'文字"
    
    print(f"📝 简单输入: {simple_input}")
    
    result = await agent.convert_to_creatomate_json(simple_input)
    
    if result:
        print("✅ 简单测试成功")
        analyze_result(result)
    else:
        print("❌ 简单测试失败")

async def main():
    """主测试函数"""
    print("🧠 直接JSON Agent测试")
    print("="*60)
    
    if not LLM_AVAILABLE:
        print("❌ LLM模块不可用，请检查依赖")
        return
    
    # 测试1: 复杂场景
    result1 = await test_real_llm_generation()
    
    # 测试2: 简单场景
    await test_simple_prompt()
    
    print("\n" + "="*60)
    if result1:
        print("🎉 真实AI调用测试成功！")
        print("✅ JSON Agent可以正常工作")
        print("✅ LLM能够生成完整的Creatomate JSON")
    else:
        print("⚠️ 测试部分失败，请检查配置")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
