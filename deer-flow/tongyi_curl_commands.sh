
# 1. 提交任务
curl -X PUT "https://tingwu.cn-beijing.aliyuncs.com/openapi/tingwu/v2/tasks?type=offline" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d @tongyi_request.json

# 2. 查询任务状态 (替换TASK_ID为实际返回的TaskId)
curl -X GET "https://tingwu.cn-beijing.aliyuncs.com/openapi/tingwu/v2/tasks/TASK_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
