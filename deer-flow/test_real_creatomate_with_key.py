#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用真实API密钥的Creatomate测试

现在可以看到真实的API返回结果了！
"""

import sys
import json
import asyncio
import logging
import requests
import time
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealCreatomateAPITester:
    """真实Creatomate API测试器"""
    
    def __init__(self):
        # 从环境变量获取API密钥
        self.api_key = os.getenv("CREATOMATE_API_KEY")
        self.base_url = "https://api.creatomate.com/v1"
        
        if not self.api_key:
            print("❌ 未找到CREATOMATE_API_KEY环境变量")
            self.ready = False
        else:
            print(f"✅ 找到API密钥: {self.api_key[:10]}...{self.api_key[-10:]}")
            self.ready = True
    
    def create_real_render(self, json_config):
        """创建真实渲染任务"""
        print("🎬 调用真实Creatomate API...")
        
        if not self.ready:
            print("❌ API未就绪")
            return None
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            print("📡 发送API请求...")
            response = requests.post(
                f"{self.base_url}/renders",
                headers=headers,
                json=json_config,
                timeout=30
            )
            
            print(f"📊 API响应状态: {response.status_code}")
            
            if response.status_code in [200, 202]:  # 202 = Accepted
                result = response.json()
                print("🎉 真实API调用成功！")
                
                # 如果返回的是数组，取第一个元素
                if isinstance(result, list) and len(result) > 0:
                    result = result[0]
                
                return result
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"📋 错误详情: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 网络请求失败: {str(e)}")
            return None
    
    def get_real_render_status(self, render_id):
        """获取真实渲染状态"""
        if not self.ready:
            return None
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
        }
        
        try:
            response = requests.get(
                f"{self.base_url}/renders/{render_id}",
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 状态查询出错: {str(e)}")
            return None
    
    def monitor_real_render_progress(self, render_id, max_wait_time=300):
        """监控真实渲染进度"""
        print(f"⏳ 开始监控真实渲染进度...")
        print(f"🆔 任务ID: {render_id}")
        
        start_time = time.time()
        attempt = 0
        
        while time.time() - start_time < max_wait_time:
            attempt += 1
            status_info = self.get_real_render_status(render_id)
            
            if not status_info:
                print("❌ 无法获取状态信息")
                break
            
            status = status_info.get("status", "unknown")
            progress = status_info.get("progress", 0)
            
            print(f"📊 [{attempt}] 状态: {status}, 进度: {progress*100:.1f}%")
            
            if status == "succeeded":
                print("🎉 真实渲染完成！")
                print("="*50)
                print("📋 真实视频输出信息:")
                
                output = status_info.get("output", {})
                print(f"• 真实下载URL: {output.get('url', '无')}")
                print(f"• 分辨率: {output.get('width', '?')}x{output.get('height', '?')}")
                print(f"• 时长: {output.get('duration', '?')}秒")
                print(f"• 文件大小: {output.get('file_size', 0)/1024/1024:.2f}MB")
                print(f"• 格式: {output.get('format', '?')}")
                if 'thumbnail' in output:
                    print(f"• 缩略图: {output.get('thumbnail')}")
                
                print("\n🔗 完整真实API响应:")
                print(json.dumps(status_info, ensure_ascii=False, indent=2))
                return status_info
                
            elif status == "failed":
                error = status_info.get("error", "未知错误")
                print(f"❌ 渲染失败: {error}")
                return status_info
                
            elif status in ["queued", "rendering"]:
                time.sleep(5)  # 等待5秒后再次查询
                continue
            else:
                print(f"⚠️ 未知状态: {status}")
                break
        
        print("⏰ 监控超时")
        return None

async def test_complete_real_workflow():
    """测试完整的真实工作流程"""
    print("🚀 完整真实Creatomate工作流程测试")
    print("="*60)
    
    try:
        # 步骤1: AI生成JSON
        print("🧠 步骤1: AI生成JSON配置")
        from src.config.configuration import Configuration
        from src.tools.video.creatomate_json_agent import convert_to_creatomate_json
        
        config = Configuration()
        
        # 简化测试输入（避免太长的视频消耗配额）
        test_input = {
            "scenes": [
                {
                    "duration": 3,
                    "background_video": "https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/30399726600-1-192.mp4",
                    "subtitle": "测试AI生成视频"
                }
            ]
        }
        
        print("📝 测试输入:")
        print(json.dumps(test_input, ensure_ascii=False, indent=2))
        
        ai_json = await convert_to_creatomate_json(config, test_input)
        
        if not ai_json:
            print("❌ AI JSON生成失败")
            return
        
        print("✅ AI JSON生成成功")
        print("📋 生成的JSON:")
        print(json.dumps(ai_json, ensure_ascii=False, indent=2)[:800] + "...")
        
        # 步骤2: 调用真实API
        print("\n" + "="*60)
        print("🎬 步骤2: 调用真实Creatomate API")
        
        api_tester = RealCreatomateAPITester()
        
        if not api_tester.ready:
            print("❌ API未就绪")
            return
        
        render_result = api_tester.create_real_render(ai_json)
        
        if not render_result:
            print("❌ 渲染任务创建失败")
            return
        
        render_id = render_result.get("id")
        print(f"✅ 真实渲染任务已创建")
        print("📋 真实API返回:")
        print(json.dumps(render_result, ensure_ascii=False, indent=2))
        
        # 步骤3: 监控真实进度
        print("\n" + "="*60)
        print("⏳ 步骤3: 监控真实渲染进度")
        
        final_result = api_tester.monitor_real_render_progress(render_id)
        
        if final_result and final_result.get("status") == "succeeded":
            print("\n🎉 完整真实工作流程成功！")
            return final_result
        else:
            print("❌ 渲染未成功完成")
            return None
            
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        logger.error(f"详细错误: {str(e)}", exc_info=True)
        return None

def quick_api_test():
    """快速API连接测试"""
    print("🔌 快速API连接测试")
    print("="*40)
    
    api_key = os.getenv("CREATOMATE_API_KEY")
    
    if not api_key:
        print("❌ 未找到API密钥")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
    }
    
    try:
        # 测试简单的JSON配置
        simple_config = {
            "source": {
                "output_format": "mp4",
                "width": 1280,
                "height": 720,
                "duration": 2,
                "elements": [
                    {
                        "id": "test_text",
                        "type": "text",
                        "track": 1,
                        "time": 0,
                        "duration": 2,
                        "text": "API测试",
                        "font_size": 50,
                        "fill_color": "#ffffff",
                        "x": "50%",
                        "y": "50%"
                    }
                ]
            }
        }
        
        # 测试API连接（使用POST创建渲染）
        response = requests.post(
            "https://api.creatomate.com/v1/renders",
            headers={**headers, "Content-Type": "application/json"},
            json=simple_config,
            timeout=10
        )
        
        print(f"📡 API连接状态: {response.status_code}")
        
        if response.status_code in [200, 202]:  # 202 = Accepted (正常)
            print("✅ API连接成功！")
            result = response.json()
            
            # 如果返回的是数组，取第一个元素
            if isinstance(result, list) and len(result) > 0:
                result = result[0]
            
            render_id = result.get("id", "未知")
            status = result.get("status", "未知")
            url = result.get("url", "无")
            
            print(f"📊 真实渲染任务ID: {render_id}")
            print(f"📊 任务状态: {status}")
            print(f"📊 输出URL: {url}")
            
            return True
        else:
            print(f"❌ API连接失败 ({response.status_code}): {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🎬 真实Creatomate API测试 - 使用您的API密钥")
    print("="*60)
    
    # 快速连接测试
    if not quick_api_test():
        print("❌ API连接失败，无法继续测试")
        return
    
    print("\n" + "="*60)
    
    # 完整工作流程测试
    result = await test_complete_real_workflow()
    
    print("\n" + "="*60)
    if result:
        print("🎉 真实API测试完成！")
        print("✅ 您看到的都是真实的API返回结果！")
        print("✅ 包括真实的视频下载URL！")
        print("✅ AI JSON生成器完美工作！")
    else:
        print("⚠️ 测试未完全成功")
        print("💡 可能原因:")
        print("  • API配额不足")
        print("  • 网络连接问题")
        print("  • 渲染时间较长")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
