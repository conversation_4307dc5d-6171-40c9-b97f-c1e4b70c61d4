#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行音频分割 - 一键执行
"""

from ffmpeg_audio_splitter import split_audio_by_speaker
import os

def main():
    """快速运行音频分割"""
    print("=" * 60)
    print("音频分割工具 - 按说话人分别生成1分钟音频")
    print("=" * 60)
    
    # 检查JSON文件是否存在
    json_file = "real_tool_call_result.json"
    if not os.path.exists(json_file):
        print(f"错误: 找不到文件 {json_file}")
        print("请确保该文件在当前目录中")
        return
    
    # 运行分割
    try:
        split_audio_by_speaker(
            json_file=json_file,
            output_dir="speaker_audio",
            target_duration=60  # 1分钟
        )
        
        print("\n" + "=" * 60)
        print("分割完成! 请查看 speaker_audio 目录中的音频文件")
        print("=" * 60)
        
    except ImportError as e:
        print(f"错误: 缺少依赖包")
        print("请运行: pip install pydub")
        print(f"详细错误: {e}")
    except Exception as e:
        print(f"运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
