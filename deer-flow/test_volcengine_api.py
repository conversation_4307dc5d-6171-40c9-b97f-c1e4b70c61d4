#!/usr/bin/env python3
"""
测试火山引擎API配置
"""

import logging
import os
import requests
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_volcengine_api_config():
    """测试火山引擎API配置"""
    logger.info("🔑 测试火山引擎API配置...")
    
    # 获取API配置
    api_key = os.getenv("VOLCENGINE_SUBTITLE_KEY") or os.getenv("VOLCENGINE_API_KEY")
    appid = os.getenv("VOLCENGINE_SUBTITLE_APPID") or os.getenv("VOLCENGINE_APPID")
    
    logger.info(f"📋 AppID: {appid}")
    if api_key:
        logger.info(f"📋 API Key: {api_key[:20]}...{api_key[-10:] if len(api_key) > 30 else api_key}")
    else:
        logger.info("📋 API Key: None")
    
    if not api_key or not appid:
        logger.error("❌ API配置缺失")
        logger.info("💡 请设置环境变量:")
        logger.info("   export VOLCENGINE_SUBTITLE_KEY='your_api_key'")
        logger.info("   export VOLCENGINE_SUBTITLE_APPID='your_appid'")
        return False
    
    # 测试一个简单的音频URL
    test_audio_url = "https://www.iflyrec.com/AudioStreamService/v1/outLinks/2a7e5980004145ea8a01fb73e88b4ddd?action=play"
    
    # 构建请求
    submit_url = "https://openspeech.bytedance.com/api/v1/vc/submit"
    
    headers = {
        "Authorization": f"Bearer; {api_key}",
        "Content-Type": "application/json"
    }
    
    params = {
        "appid": appid,
        "token": api_key,
        "language": "zh-CN",
        "use_itn": "true",
        "max_lines": 1,
        "words_per_line": 15
    }
    
    payload = {
        "url": test_audio_url
    }
    
    logger.info("🚀 发送测试请求...")
    logger.info(f"📋 URL: {submit_url}")
    logger.info(f"📋 参数: {params}")
    logger.info(f"📋 Headers: {headers}")
    logger.info(f"📋 Payload: {payload}")
    
    try:
        response = requests.post(
            submit_url,
            headers=headers,
            params=params,
            json=payload,
            timeout=30
        )
        
        logger.info(f"📊 响应状态码: {response.status_code}")
        logger.info(f"📊 响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            logger.info(f"📊 响应内容: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
        except:
            logger.info(f"📊 响应内容 (原始): {response.text}")
        
        if response.status_code == 200:
            logger.info("✅ API请求成功")
            return True
        elif response.status_code == 401:
            logger.error("❌ 401 Unauthorized - 认证失败")
            logger.info("💡 可能的原因:")
            logger.info("   • API Key无效或过期")
            logger.info("   • AppID与API Key不匹配")
            logger.info("   • 鉴权格式不正确")
            return False
        else:
            logger.error(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ API请求异常: {e}")
        return False

def test_different_auth_formats():
    """测试不同的鉴权格式"""
    logger.info("🔄 测试不同的鉴权格式...")
    
    api_key = os.getenv("VOLCENGINE_SUBTITLE_KEY") or os.getenv("VOLCENGINE_API_KEY")
    appid = os.getenv("VOLCENGINE_SUBTITLE_APPID") or os.getenv("VOLCENGINE_APPID")
    
    if not api_key or not appid:
        logger.warning("⚠️ 跳过鉴权格式测试（缺少API配置）")
        return False
    
    test_audio_url = "https://www.iflyrec.com/AudioStreamService/v1/outLinks/2a7e5980004145ea8a01fb73e88b4ddd?action=play"
    submit_url = "https://openspeech.bytedance.com/api/v1/vc/submit"
    
    # 测试不同的鉴权格式
    auth_formats = [
        ("Bearer; {token}", {"appid": appid, "language": "zh-CN"}),
        ("Bearer {token}", {"appid": appid, "language": "zh-CN"}),
        ("Bearer; {token}", {"appid": appid, "token": api_key, "language": "zh-CN"}),
        ("Bearer {token}", {"appid": appid, "token": api_key, "language": "zh-CN"}),
    ]
    
    for i, (auth_format, params) in enumerate(auth_formats, 1):
        logger.info(f"\n🧪 测试格式 {i}: {auth_format}")
        
        headers = {
            "Authorization": auth_format.format(token=api_key),
            "Content-Type": "application/json"
        }
        
        payload = {"url": test_audio_url}
        
        try:
            response = requests.post(
                submit_url,
                headers=headers,
                params=params,
                json=payload,
                timeout=10
            )
            
            logger.info(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                logger.info("   ✅ 成功！")
                return True
            elif response.status_code == 401:
                logger.info("   ❌ 401 Unauthorized")
            else:
                logger.info(f"   ❌ 其他错误: {response.status_code}")
                
        except Exception as e:
            logger.info(f"   ❌ 请求异常: {e}")
    
    return False

def main():
    """主函数"""
    logger.info("🎯 开始火山引擎API配置测试...")
    
    # 基础配置测试
    config_ok = test_volcengine_api_config()
    
    if not config_ok:
        # 尝试不同的鉴权格式
        auth_ok = test_different_auth_formats()
        
        if not auth_ok:
            logger.error("\n❌ 所有鉴权格式测试都失败了")
            logger.info("\n💡 建议:")
            logger.info("   1. 检查API Key是否正确")
            logger.info("   2. 检查AppID是否正确")
            logger.info("   3. 确认API Key没有过期")
            logger.info("   4. 联系火山引擎技术支持")
        else:
            logger.info("\n✅ 找到了正确的鉴权格式！")
    else:
        logger.info("\n✅ API配置测试成功！")

if __name__ == "__main__":
    main()
