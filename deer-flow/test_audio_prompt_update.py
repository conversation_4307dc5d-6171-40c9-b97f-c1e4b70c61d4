#!/usr/bin/env python3
"""
测试音频专家提示词更新
"""

import logging
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_audio_prompt_content():
    """测试音频专家提示词内容更新"""
    logger.info("🎵 测试音频专家提示词内容更新...")
    
    try:
        # 读取音频专家提示词文件
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/audio_creator_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键内容
        content_checks = {
            "包含视频字幕提取工具": "video_subtitle_extraction" in content,
            "包含AI二创专用工具": "AI二创专用工具" in content,
            "包含视频字幕提取场景": "视频字幕提取场景" in content,
            "包含赵本山示例": "赵本山" in content,
            "包含声音克隆素材": "声音克隆素材" in content,
            "包含COS存储": "save_to_cos" in content,
            "包含音频片段提取": "extract_audio_segments" in content,
            "包含说话人识别": "auto_speaker_identification" in content,
            "包含任务示例4": "任务示例4" in content,
            "包含ASSETS更新": "subtitle_data" in content and "cos_urls" in content
        }
        
        logger.info("🔍 音频专家提示词内容检查:")
        all_good = True
        for check_name, passed in content_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                all_good = False
        
        # 统计关键词出现次数
        keyword_counts = {
            "video_subtitle_extraction": content.count("video_subtitle_extraction"),
            "AI二创": content.count("AI二创"),
            "赵本山": content.count("赵本山"),
            "声音克隆": content.count("声音克隆"),
            "视频字幕": content.count("视频字幕")
        }
        
        logger.info("📊 关键词统计:")
        for keyword, count in keyword_counts.items():
            logger.info(f"   {keyword}: {count} 次")
        
        return all_good
        
    except Exception as e:
        logger.error(f"❌ 音频专家提示词内容测试失败: {e}")
        return False

def test_audio_prompt_structure():
    """测试音频专家提示词结构"""
    logger.info("🏗️ 测试音频专家提示词结构...")
    
    try:
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/audio_creator_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查工具描述部分
        tool_sections = [
            "🎭 多人对话音频生成 (Multi-Speaker TTS)",
            "🎬 视频字幕提取与音频分析 (Video Subtitle Extraction)",
            "🎵 音乐生成 (Suno Music Generation)",
            "🎤 文本转语音 (Text-to-Speech)",
            "🔊 声音克隆 (Voice Cloning)"
        ]
        
        structure_checks = {}
        for section in tool_sections:
            structure_checks[section] = section in content
        
        logger.info("🔍 工具描述章节检查:")
        structure_good = True
        for section, exists in structure_checks.items():
            status = "✅" if exists else "❌"
            logger.info(f"   {status} {section}")
            if not exists:
                structure_good = False
        
        # 检查场景指南
        scenario_checks = {
            "视频字幕提取场景": "视频字幕提取场景" in content,
            "音色克隆场景": "音色克隆场景" in content,
            "多人对话场景": "多人对话场景" in content,
            "音乐生成场景": "音乐生成场景" in content
        }
        
        logger.info("🔍 场景指南检查:")
        for check_name, passed in scenario_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                structure_good = False
        
        return structure_good
        
    except Exception as e:
        logger.error(f"❌ 音频专家提示词结构测试失败: {e}")
        return False

def test_audio_prompt_examples():
    """测试音频专家提示词示例"""
    logger.info("💡 测试音频专家提示词示例...")
    
    try:
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/audio_creator_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查任务示例
        example_checks = {
            "包含任务示例4": "任务示例4" in content,
            "包含AI二创示例": "AI二创视频字幕提取" in content,
            "包含参数构建示例": "media_url" in content and "auto_speaker_identification" in content,
            "包含工具选择逻辑": "工具选择" in content and "video_subtitle_extraction" in content,
            "包含情景感知": "情景感知与任务解析" in content,
            "包含创意构思": "创意构思" in content
        }
        
        logger.info("🔍 任务示例检查:")
        examples_good = True
        for check_name, passed in example_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                examples_good = False
        
        # 检查JSON示例
        json_blocks = re.findall(r'```json[\s\S]*?```', content)
        logger.info(f"📊 JSON示例数量: {len(json_blocks)}")
        
        # 检查是否包含视频字幕提取的JSON示例
        has_video_subtitle_json = any("media_url" in block for block in json_blocks)
        logger.info(f"🎬 包含视频字幕提取JSON示例: {'✅' if has_video_subtitle_json else '❌'}")
        
        return examples_good and has_video_subtitle_json
        
    except Exception as e:
        logger.error(f"❌ 音频专家提示词示例测试失败: {e}")
        return False

def test_audio_prompt_integration():
    """测试音频专家提示词集成效果"""
    logger.info("🔗 测试音频专家提示词集成效果...")
    
    try:
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/audio_creator_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查工具集成
        integration_checks = {
            "工具列表完整": "video_subtitle_extraction" in content and "multi_speaker_tts" in content,
            "场景指南完整": "视频字幕提取场景" in content and "立即调用" in content,
            "参数说明完整": "media_url" in content and "extract_audio_segments" in content,
            "ASSETS格式更新": "subtitle_data" in content and "audio_segments" in content,
            "AI二创工作流": "AI二创项目" in content and "声音克隆素材准备" in content
        }
        
        logger.info("🔍 集成效果检查:")
        integration_good = True
        for check_name, passed in integration_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                integration_good = False
        
        # 检查文档质量
        lines = content.split('\n')
        total_lines = len(lines)
        non_empty_lines = len([line for line in lines if line.strip()])
        
        logger.info(f"📏 文档统计:")
        logger.info(f"   总行数: {total_lines}")
        logger.info(f"   非空行数: {non_empty_lines}")
        logger.info(f"   字符数: {len(content)}")
        
        # 检查是否正确定位为音频专家工具
        audio_expert_checks = {
            "音频专家身份": "音频创作专家" in content or "Audio Creator" in content,
            "音频工具集成": "音频生成" in content and "声音克隆" in content,
            "视频音频提取": "视频字幕提取" in content and "音频分析" in content
        }
        
        logger.info("🎵 音频专家定位检查:")
        for check_name, passed in audio_expert_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                integration_good = False
        
        return integration_good
        
    except Exception as e:
        logger.error(f"❌ 音频专家提示词集成测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始音频专家提示词更新测试...")
    logger.info("目标: 验证视频字幕提取工具在音频专家中的集成")
    
    tests = [
        ("音频专家提示词内容", test_audio_prompt_content),
        ("音频专家提示词结构", test_audio_prompt_structure),
        ("音频专家提示词示例", test_audio_prompt_examples),
        ("音频专家提示词集成效果", test_audio_prompt_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 音频专家提示词更新测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("\n🎉 音频专家提示词更新完全成功！")
        logger.info("")
        logger.info("🚀 **更新内容:**")
        logger.info("   ✅ 添加视频字幕提取工具到音频专家工具集")
        logger.info("   ✅ 新增视频字幕提取场景指南")
        logger.info("   ✅ 添加AI二创任务示例4")
        logger.info("   ✅ 更新ASSETS格式支持字幕数据")
        logger.info("   ✅ 完整的参数说明和使用指导")
        logger.info("")
        logger.info("🎵 **音频专家现在可以:**")
        logger.info("   • 识别视频字幕提取需求")
        logger.info("   • 处理AI二创项目的音频分析")
        logger.info("   • 提取视频中的对话和音频片段")
        logger.info("   • 为声音克隆准备素材")
        logger.info("   • 与其他音频工具配合使用")
        logger.info("")
        logger.info("🎭 **AI二创工作流:**")
        logger.info("   1. 用户: '提取赵本山小品的对话做AI二创'")
        logger.info("   2. 主代理: 识别为音频任务，转发给音频专家")
        logger.info("   3. 音频专家: 使用video_subtitle_extraction工具")
        logger.info("   4. 返回: 结构化字幕 + 音频片段 + COS存储URL")
        logger.info("")
        logger.info("🎤 **音频专家提示词优化完成！**")
        logger.info("现在音频专家完全支持视频字幕提取和AI二创场景！")
    else:
        logger.warning("⚠️ 部分测试失败，音频专家提示词可能需要进一步优化")

if __name__ == "__main__":
    main()
