#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版音频分割器 - 专门处理real_tool_call_result.json格式
"""

import json
import os
from pathlib import Path
from pydub import AudioSegment


def split_audio_by_speaker(json_file="real_tool_call_result.json", 
                          output_dir="speaker_audio", 
                          target_duration=60):
    """
    根据说话人分割音频
    
    Args:
        json_file: JSON文件路径
        output_dir: 输出目录
        target_duration: 每个说话人的目标时长（秒）
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 读取JSON文件
    print(f"读取文件: {json_file}")
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取音频片段信息
    audio_segments = data.get('audio_segments', {})
    if not audio_segments:
        print("错误: 未找到audio_segments数据")
        return
    
    # 按说话人分组
    speaker_groups = {}
    for subtitle in data['subtitles']:
        speaker_id = subtitle['speaker_id']
        if speaker_id not in speaker_groups:
            speaker_groups[speaker_id] = []
        speaker_groups[speaker_id].append(subtitle)
    
    print(f"发现 {len(speaker_groups)} 个说话人")
    print(f"目标时长: {target_duration} 秒")
    print("-" * 50)
    
    # 为每个说话人创建音频
    for speaker_id, subtitles in speaker_groups.items():
        print(f"\n处理说话人 {speaker_id}")
        
        # 按时间排序
        subtitles.sort(key=lambda x: x['start_time'])
        
        # 创建拼接音频
        combined_audio = AudioSegment.empty()
        current_duration = 0
        target_ms = target_duration * 1000
        
        # 获取该说话人的音频片段
        speaker_name = subtitles[0]['speaker_name']
        segments = audio_segments.get(speaker_name, {}).get('segments', [])
        
        print(f"  说话人名称: {speaker_name}")
        print(f"  可用片段数: {len(segments)}")
        
        for i, subtitle in enumerate(subtitles):
            if current_duration >= target_ms:
                break
            
            # 查找匹配的音频文件
            audio_file = None
            start_time_sec = subtitle['start_time'] / 1000
            end_time_sec = subtitle['end_time'] / 1000
            
            for segment in segments:
                # 允许0.5秒的时间误差
                if (abs(segment['start_time'] - start_time_sec) < 0.5 and 
                    abs(segment['end_time'] - end_time_sec) < 0.5):
                    audio_file = segment['file_path']
                    break
            
            if not audio_file:
                print(f"    警告: 片段 {i+1} 未找到对应音频文件")
                continue
            
            if not os.path.exists(audio_file):
                print(f"    警告: 音频文件不存在: {audio_file}")
                continue
            
            try:
                # 加载音频片段
                audio_segment = AudioSegment.from_wav(audio_file)
                
                # 检查是否会超过目标时长
                remaining_time = target_ms - current_duration
                if len(audio_segment) > remaining_time:
                    audio_segment = audio_segment[:remaining_time]
                
                # 添加到合并音频
                combined_audio += audio_segment
                current_duration += len(audio_segment)
                
                print(f"    添加片段 {i+1}: {subtitle['text'][:20]}... "
                      f"({len(audio_segment)/1000:.1f}秒)")
                
            except Exception as e:
                print(f"    错误: 无法处理音频文件 {audio_file}: {e}")
                continue
        
        # 保存音频文件
        if len(combined_audio) > 0:
            final_duration = len(combined_audio) / 1000
            output_file = output_path / f"说话人{speaker_id}_{speaker_name}_{final_duration:.1f}秒.wav"
            
            combined_audio.export(str(output_file), format="wav")
            print(f"  已保存: {output_file} (时长: {final_duration:.1f}秒)")
        else:
            print(f"  警告: 说话人 {speaker_id} 没有有效的音频数据")
    
    print(f"\n完成! 所有音频文件已保存到: {output_path}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="简化版音频分割器")
    parser.add_argument("-j", "--json", default="real_tool_call_result.json",
                       help="JSON文件路径 (默认: real_tool_call_result.json)")
    parser.add_argument("-o", "--output", default="speaker_audio",
                       help="输出目录 (默认: speaker_audio)")
    parser.add_argument("-d", "--duration", type=int, default=60,
                       help="目标时长（秒，默认: 60）")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json):
        print(f"错误: JSON文件不存在: {args.json}")
        return
    
    try:
        split_audio_by_speaker(args.json, args.output, args.duration)
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
