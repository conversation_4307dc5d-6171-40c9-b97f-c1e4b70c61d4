#!/usr/bin/env python3
"""
测试提示词更新
"""

import logging
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_prompt_content():
    """测试提示词内容更新"""
    logger.info("📝 测试提示词内容更新...")
    
    try:
        # 读取提示词文件
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/master_agent_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键内容
        content_checks = {
            "包含视频字幕提取工具": "video_subtitle_extraction" in content,
            "包含音频理解工具": "audio_understanding" in content,
            "包含内容提取分类": "内容提取" in content,
            "包含AI二创项目示例": "AI二创项目" in content,
            "包含视频字幕提取指南": "视频字幕提取工具使用指南" in content,
            "包含AI二创工作流": "视频字幕提取与AI二创工作流" in content,
            "包含工具配合使用": "与其他工具的配合使用" in content,
            "包含赵本山示例": "赵本山" in content,
            "包含COS存储说明": "save_to_cos" in content,
            "包含声音克隆场景": "声音克隆" in content
        }
        
        logger.info("🔍 提示词内容检查:")
        all_good = True
        for check_name, passed in content_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                all_good = False
        
        # 统计关键词出现次数
        keyword_counts = {
            "video_subtitle_extraction": content.count("video_subtitle_extraction"),
            "AI二创": content.count("AI二创"),
            "赵本山": content.count("赵本山"),
            "声音克隆": content.count("声音克隆"),
            "COS": content.count("COS")
        }
        
        logger.info("📊 关键词统计:")
        for keyword, count in keyword_counts.items():
            logger.info(f"   {keyword}: {count} 次")
        
        return all_good
        
    except Exception as e:
        logger.error(f"❌ 提示词内容测试失败: {e}")
        return False

def test_prompt_structure():
    """测试提示词结构"""
    logger.info("🏗️ 测试提示词结构...")
    
    try:
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/master_agent_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查章节结构
        sections = [
            "# 主控Agent - 核心提示词",
            "## 角色和目标",
            "## 你的工作流程",
            "## 可用工具",
            "## 增强的执行模式",
            "## 内容理解工具使用指南",
            "## 视频字幕提取工具使用指南"
        ]
        
        structure_checks = {}
        for section in sections:
            structure_checks[section] = section in content
        
        logger.info("🔍 章节结构检查:")
        structure_good = True
        for section, exists in structure_checks.items():
            status = "✅" if exists else "❌"
            logger.info(f"   {status} {section}")
            if not exists:
                structure_good = False
        
        # 检查新增章节的详细内容
        new_section_checks = {
            "何时使用视频字幕提取工具": "何时使用视频字幕提取工具" in content,
            "视频字幕提取工具详细说明": "视频字幕提取工具详细说明" in content,
            "视频字幕提取与AI二创工作流": "视频字幕提取与AI二创工作流" in content,
            "输出结果理解": "输出结果理解" in content,
            "与其他工具的配合使用": "与其他工具的配合使用" in content,
            "使用建议": "使用建议" in content
        }
        
        logger.info("🔍 新增章节检查:")
        for check_name, passed in new_section_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                structure_good = False
        
        return structure_good
        
    except Exception as e:
        logger.error(f"❌ 提示词结构测试失败: {e}")
        return False

def test_prompt_examples():
    """测试提示词示例"""
    logger.info("💡 测试提示词示例...")
    
    try:
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/master_agent_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查示例代码块
        code_blocks = re.findall(r'```[\s\S]*?```', content)
        
        example_checks = {
            "包含视频字幕提取示例": any("video_subtitle_extraction" in block for block in code_blocks),
            "包含AI二创流程示例": any("AI二创" in block for block in code_blocks),
            "包含参数示例": any("media_url" in block for block in code_blocks),
            "包含输出结果示例": any("cos_urls" in block for block in code_blocks),
            "包含工具配合示例": any("audio_expert" in block for block in code_blocks)
        }
        
        logger.info("🔍 示例代码检查:")
        examples_good = True
        for check_name, passed in example_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                examples_good = False
        
        logger.info(f"📊 代码块总数: {len(code_blocks)}")
        
        # 检查具体的使用场景
        use_cases = [
            "提取这个赵本山小品的对话",
            "从这个视频中提取音频片段用于声音克隆",
            "给这个视频生成中文字幕",
            "分析这个视频中的说话人和对话内容"
        ]
        
        logger.info("🎭 使用场景检查:")
        for use_case in use_cases:
            exists = use_case in content
            status = "✅" if exists else "❌"
            logger.info(f"   {status} {use_case}")
        
        return examples_good
        
    except Exception as e:
        logger.error(f"❌ 提示词示例测试失败: {e}")
        return False

def test_prompt_integration():
    """测试提示词集成效果"""
    logger.info("🔗 测试提示词集成效果...")
    
    try:
        with open('/Users/<USER>/openArt-1/deer-flow/src/prompts/master_agent_prompt.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查工具集成
        integration_checks = {
            "工具列表更新": "video_subtitle_extraction" in content and "内容提取" in content,
            "任务复杂度分析更新": "内容提取" in content and "AI二创项目" in content,
            "工作流程集成": "视频字幕提取与AI二创工作流" in content,
            "与现有工具配合": "与音频专家配合" in content and "与视频专家配合" in content,
            "完整使用指南": "使用建议" in content and "输出结果理解" in content
        }
        
        logger.info("🔍 集成效果检查:")
        integration_good = True
        for check_name, passed in integration_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                integration_good = False
        
        # 检查文档长度和质量
        lines = content.split('\n')
        total_lines = len(lines)
        non_empty_lines = len([line for line in lines if line.strip()])
        
        logger.info(f"📏 文档统计:")
        logger.info(f"   总行数: {total_lines}")
        logger.info(f"   非空行数: {non_empty_lines}")
        logger.info(f"   字符数: {len(content)}")
        
        # 质量检查
        quality_checks = {
            "文档长度合理": 500 <= total_lines <= 800,
            "内容充实": non_empty_lines >= 400,
            "字符数合理": 20000 <= len(content) <= 50000
        }
        
        logger.info("📊 质量检查:")
        for check_name, passed in quality_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                integration_good = False
        
        return integration_good
        
    except Exception as e:
        logger.error(f"❌ 提示词集成测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始提示词更新测试...")
    logger.info("目标: 验证视频字幕提取工具的提示词集成")
    
    tests = [
        ("提示词内容", test_prompt_content),
        ("提示词结构", test_prompt_structure),
        ("提示词示例", test_prompt_examples),
        ("提示词集成效果", test_prompt_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 提示词更新测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("\n🎉 提示词更新完全成功！")
        logger.info("")
        logger.info("🚀 **更新内容:**")
        logger.info("   ✅ 添加视频字幕提取工具到工具列表")
        logger.info("   ✅ 更新任务复杂度分析（内容提取、AI二创项目）")
        logger.info("   ✅ 新增视频字幕提取工具使用指南")
        logger.info("   ✅ 详细的AI二创工作流说明")
        logger.info("   ✅ 与其他工具的配合使用示例")
        logger.info("   ✅ 完整的参数说明和使用建议")
        logger.info("")
        logger.info("🎭 **AI二创场景覆盖:**")
        logger.info("   • 赵本山小品AI二创")
        logger.info("   • 声音克隆素材准备")
        logger.info("   • 视频字幕生成")
        logger.info("   • 对话内容分析")
        logger.info("   • 音频片段提取")
        logger.info("")
        logger.info("🔧 **主代理现在知道如何:**")
        logger.info("   • 识别视频字幕提取需求")
        logger.info("   • 选择合适的工具参数")
        logger.info("   • 与其他专家工具配合")
        logger.info("   • 处理AI二创项目流程")
        logger.info("   • 管理COS存储选项")
        logger.info("")
        logger.info("🎤 **提示词优化完成！**")
        logger.info("主代理现在完全了解视频字幕提取工具的使用方法！")
    else:
        logger.warning("⚠️ 部分测试失败，提示词可能需要进一步优化")

if __name__ == "__main__":
    main()
