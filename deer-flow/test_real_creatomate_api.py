#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实Creatomate API调用测试

需要配置真实API密钥才能看到真实返回结果
"""

import sys
import json
import asyncio
import logging
import requests
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_with_real_api_key():
    """使用真实API密钥测试"""
    print("🔑 真实Creatomate API调用测试")
    print("="*50)
    
    # 这里需要您的真实API密钥
    API_KEY = "YOUR_CREATOMATE_API_KEY_HERE"  # 替换为您的真实API密钥
    
    if API_KEY == "YOUR_CREATOMATE_API_KEY_HERE":
        print("❌ 请先配置您的真实Creatomate API密钥")
        print("📝 步骤：")
        print("1. 注册Creatomate账户: https://creatomate.com")
        print("2. 获取API密钥: https://creatomate.com/docs/api/introduction")
        print("3. 在代码中替换 YOUR_CREATOMATE_API_KEY_HERE")
        return
    
    # 使用我们AI生成的JSON配置
    ai_generated_json = {
        "source": {
            "output_format": "mp4",
            "width": 1920,
            "height": 1080,
            "duration": 7,
            "elements": [
                {
                    "id": "background_video",
                    "type": "video",
                    "track": 1,
                    "time": 0,
                    "duration": 7,
                    "source": "https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/30399726600-1-192.mp4",
                    "volume": "0%",
                    "x": "50%",
                    "y": "50%",
                    "width": "100%",
                    "height": "100%"
                },
                {
                    "id": "main_audio",
                    "type": "audio",
                    "track": 4,
                    "time": 0,
                    "duration": 7,
                    "source": "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/deerflow_assets/赵本山小品之相当有钱_爱给网_aigei_com.mp3",
                    "volume": "85%"
                },
                {
                    "id": "subtitle_1",
                    "type": "text",
                    "track": 5,
                    "time": 0,
                    "duration": 3,
                    "text": "现在有钱",
                    "font_family": "Microsoft YaHei",
                    "font_size": 80,
                    "fill_color": "#ffffff",
                    "background_color": "rgba(0,0,0,0.6)",
                    "x": "50%",
                    "y": "85%"
                },
                {
                    "id": "subtitle_2", 
                    "type": "text",
                    "track": 5,
                    "time": 3,
                    "duration": 4,
                    "text": "瞅着穿的相当有钱",
                    "font_family": "Microsoft YaHei",
                    "font_size": 80,
                    "fill_color": "#ffffff",
                    "background_color": "rgba(0,0,0,0.6)",
                    "x": "50%",
                    "y": "85%"
                }
            ]
        }
    }
    
    print("📋 使用AI生成的JSON配置:")
    print(json.dumps(ai_generated_json, ensure_ascii=False, indent=2)[:500] + "...")
    
    # 调用真实API
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        print("\n🚀 调用真实Creatomate API...")
        response = requests.post(
            "https://api.creatomate.com/v1/renders",
            headers=headers,
            json=ai_generated_json,
            timeout=30
        )
        
        print(f"📡 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 真实API调用成功！")
            print("📋 真实API返回:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            render_id = result.get("id")
            print(f"\n🆔 渲染任务ID: {render_id}")
            
            # 监控真实渲染进度
            monitor_real_progress(API_KEY, render_id)
            
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def monitor_real_progress(api_key, render_id):
    """监控真实渲染进度"""
    print(f"\n⏳ 监控真实渲染进度...")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
    }
    
    max_attempts = 60  # 最多查询60次（5分钟）
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(
                f"https://api.creatomate.com/v1/renders/{render_id}",
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                status_info = response.json()
                status = status_info.get("status", "unknown")
                progress = status_info.get("progress", 0)
                
                print(f"📊 [{attempt+1}/60] 状态: {status}, 进度: {progress*100:.1f}%")
                
                if status == "succeeded":
                    print("🎉 真实渲染完成！")
                    output = status_info.get("output", {})
                    
                    print("📋 真实视频输出:")
                    print(f"• 真实下载URL: {output.get('url', '无')}")
                    print(f"• 分辨率: {output.get('width', '?')}x{output.get('height', '?')}")
                    print(f"• 时长: {output.get('duration', '?')}秒")
                    print(f"• 文件大小: {output.get('file_size', 0)/1024/1024:.1f}MB")
                    print(f"• 缩略图: {output.get('thumbnail', '无')}")
                    
                    print("\n🔗 完整真实API响应:")
                    print(json.dumps(status_info, ensure_ascii=False, indent=2))
                    break
                    
                elif status == "failed":
                    error = status_info.get("error", "未知错误")
                    print(f"❌ 渲染失败: {error}")
                    break
                    
                elif status in ["queued", "rendering"]:
                    time.sleep(5)  # 等待5秒再查询
                    continue
                else:
                    print(f"⚠️ 未知状态: {status}")
                    break
                    
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
                break
                
        except Exception as e:
            print(f"❌ 查询出错: {str(e)}")
            break
    else:
        print("⏰ 监控超时（5分钟）")

def show_api_configuration_guide():
    """显示API配置指南"""
    print("\n📖 Creatomate API配置指南")
    print("="*50)
    print("🔑 获取API密钥步骤:")
    print("1. 访问: https://creatomate.com")
    print("2. 注册账户并登录")
    print("3. 进入 Dashboard -> Settings -> API Keys")
    print("4. 创建新的API密钥")
    print("5. 复制密钥并替换代码中的占位符")
    
    print("\n💰 定价信息:")
    print("• 免费试用: 通常包含少量渲染时间")
    print("• 按秒计费: 根据输出视频长度收费")
    print("• 详情: https://creatomate.com/pricing")
    
    print("\n⚠️ 注意事项:")
    print("• API密钥请妥善保管，不要泄露")
    print("• 真实渲染会消耗您的配额")
    print("• 建议先用短视频测试")

def main():
    """主函数"""
    print("🎬 真实Creatomate API测试")
    print("="*60)
    
    # 显示配置指南
    show_api_configuration_guide()
    
    print("\n" + "="*60)
    
    # 尝试真实API调用
    test_with_real_api_key()
    
    print("\n" + "="*60)
    print("📝 总结:")
    print("• 刚才的测试是模拟模式，返回的URL是示例数据")
    print("• 要看到真实结果，需要配置真实的Creatomate API密钥")
    print("• 我们的AI JSON生成器工作正常，可以生成有效配置")
    print("• 配置API密钥后，您将看到真实的视频URL和下载链接")
    print("="*60)

if __name__ == "__main__":
    main()
