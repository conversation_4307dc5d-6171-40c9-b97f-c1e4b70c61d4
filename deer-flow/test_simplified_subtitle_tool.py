#!/usr/bin/env python3
"""
测试简化后的视频字幕提取工具
"""

import logging
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_simplified_subtitle_tool():
    """测试简化后的字幕工具"""
    logger.info("🎬 测试简化后的视频字幕提取工具...")
    
    try:
        # 添加路径
        sys.path.append('/Users/<USER>/openArt-1/deer-flow/src')
        
        # 导入配置和工具
        from config.configuration import Configuration
        from tools.audio.video_subtitle_extraction import VideoSubtitleExtractionInput, get_video_subtitle_extraction_tool
        
        config = Configuration.from_runnable_config()
        logger.info("✅ 配置加载成功")
        
        # 测试输入模型
        test_input = VideoSubtitleExtractionInput(
            media_url="https://example.com/zhaobenshang.mp4",
            language="zh-CN",
            auto_speaker_identification=True,
            output_format="json",
            precision_level="sentence",  # 现在只支持sentence级别
            extract_audio_segments=True
        )
        
        logger.info("✅ 简化输入模型创建成功")
        logger.info(f"📋 精度级别: {test_input.precision_level}")
        logger.info(f"📋 输出格式: {test_input.output_format}")
        logger.info(f"📋 说话人识别: {test_input.auto_speaker_identification}")
        
        # 创建工具
        tool = get_video_subtitle_extraction_tool(config)
        
        if tool is None:
            logger.info("✅ 工具正确返回None（缺少Volcengine配置时的预期行为）")
        else:
            logger.info("✅ 工具创建成功")
            logger.info(f"📋 工具名称: {tool.name}")
            
            # 检查工具描述是否更新
            description = tool.description
            if "sentence-level" in description:
                logger.info("✅ 工具描述已更新为句子级别")
            else:
                logger.warning("⚠️ 工具描述可能未正确更新")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 简化字幕工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simplified_output_structure():
    """测试简化后的输出结构"""
    logger.info("📊 测试简化后的输出结构...")
    
    try:
        # 检查代码中是否移除了词级处理
        with open('/Users/<USER>/openArt-1/deer-flow/src/tools/audio/video_subtitle_extraction.py', 'r') as f:
            content = f.read()
        
        simplification_checks = {
            "移除词级精度选项": 'precision_level: Literal["sentence"]' in content,
            "简化输出结构": '"words": []' not in content or content.count('"words"') < 3,
            "保持句子级时间戳": '"start_time"' in content and '"end_time"' in content,
            "保持说话人信息": '"speaker_name"' in content and '"speaker_id"' in content,
            "更新工具描述": "sentence-level" in content,
            "保持音频片段提取": "_extract_audio_segments" in content
        }
        
        logger.info("🔍 简化检查:")
        all_simplified = True
        for check_name, passed in simplification_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                all_simplified = False
        
        return all_simplified
        
    except Exception as e:
        logger.error(f"❌ 简化输出结构测试失败: {e}")
        return False

def test_ai_recreation_workflow():
    """测试AI二创工作流的适用性"""
    logger.info("🎭 测试AI二创工作流适用性...")
    
    try:
        # 模拟简化后的输出结构
        simplified_output = {
            "media_info": {
                "duration": 180.5,
                "format": "mp4",
                "is_video": True
            },
            "speaker_info": {
                "1": "赵本山",
                "2": "宋丹丹"
            },
            "subtitles": [
                {
                    "speaker_id": "1",
                    "speaker_name": "赵本山",
                    "text": "这个小品啊，就是要让大家开心",
                    "start_time": 1200,
                    "end_time": 3500,
                    "duration": 2300
                },
                {
                    "speaker_id": "2", 
                    "speaker_name": "宋丹丹",
                    "text": "是啊，我们要演得自然一点",
                    "start_time": 3600,
                    "end_time": 5800,
                    "duration": 2200
                }
            ],
            "audio_segments": {
                "赵本山": [
                    {
                        "file_path": "/tmp/赵本山_001.wav",
                        "start_time": 1.2,
                        "end_time": 3.5,
                        "duration": 2.3,
                        "text": "这个小品啊，就是要让大家开心"
                    }
                ]
            },
            "statistics": {
                "total_duration": 4500,
                "speaker_stats": {
                    "赵本山": {
                        "utterance_count": 1,
                        "total_duration": 2300
                    }
                }
            }
        }
        
        # 检查AI二创工作流的关键要素
        workflow_checks = {
            "说话人清晰识别": "赵本山" in str(simplified_output["speaker_info"]),
            "句子级时间戳": all("start_time" in sub and "end_time" in sub for sub in simplified_output["subtitles"]),
            "音频片段可用": "audio_segments" in simplified_output and "赵本山" in simplified_output["audio_segments"],
            "文本内容完整": all("text" in sub and len(sub["text"]) > 0 for sub in simplified_output["subtitles"]),
            "统计信息有用": "statistics" in simplified_output and "speaker_stats" in simplified_output["statistics"],
            "结构简洁清晰": len(simplified_output["subtitles"][0]) <= 6  # 不超过6个字段，保持简洁
        }
        
        logger.info("🔍 AI二创工作流检查:")
        workflow_ready = True
        for check_name, passed in workflow_checks.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {check_name}")
            if not passed:
                workflow_ready = False
        
        if workflow_ready:
            logger.info("\n🎭 AI二创工作流示例:")
            logger.info("   1. 提取字幕 → 获得句子级对话和时间戳")
            logger.info("   2. 识别角色 → 自动区分'赵本山'、'宋丹丹'")
            logger.info("   3. 提取音频 → 为每个角色生成音频片段")
            logger.info("   4. 声音克隆 → 使用音频片段训练声音模型")
            logger.info("   5. 生成新台词 → 基于原始结构创作新内容")
            logger.info("   6. 重新合成 → 用克隆声音说新台词")
        
        return workflow_ready
        
    except Exception as e:
        logger.error(f"❌ AI二创工作流测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 开始简化后的视频字幕提取工具测试...")
    logger.info("目标: 验证句子级别的简化处理是否满足AI二创需求")
    
    tests = [
        ("简化字幕工具", test_simplified_subtitle_tool),
        ("简化输出结构", test_simplified_output_structure),
        ("AI二创工作流", test_ai_recreation_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*60}")
    logger.info("🎯 简化后的视频字幕提取工具测试总结")
    logger.info(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        logger.info("\n🎉 简化后的视频字幕提取工具完美！")
        logger.info("")
        logger.info("🚀 **简化优势:**")
        logger.info("   ✅ 句子级时间戳 - 适合AI二创的精度")
        logger.info("   ✅ 清晰的数据结构 - 易于后续处理")
        logger.info("   ✅ 保留核心功能 - 说话人识别、音频片段提取")
        logger.info("   ✅ 减少复杂性 - 移除不必要的词级细节")
        logger.info("")
        logger.info("🎭 **AI二创优化:**")
        logger.info("   • 句子级别足够进行声音克隆训练")
        logger.info("   • 简化的结构便于LLM理解和处理")
        logger.info("   • 保持了时间戳精度用于音频同步")
        logger.info("   • 专注于实用性而非过度精细化")
        logger.info("")
        logger.info("📊 **输出示例:**")
        logger.info("   {")
        logger.info('     "speaker_name": "赵本山",')
        logger.info('     "text": "这个小品啊，就是要让大家开心",')
        logger.info('     "start_time": 1200,')
        logger.info('     "end_time": 3500,')
        logger.info('     "duration": 2300')
        logger.info("   }")
        logger.info("")
        logger.info("🎤 **简化后的工具已准备就绪！**")
        logger.info("现在更适合AI二创场景，数据结构清晰实用！")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
