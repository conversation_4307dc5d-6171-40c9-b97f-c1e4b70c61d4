#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Creatomate工具Agent系统集成测试

验证工具在完整系统中的运行情况
"""

import sys
import json
import asyncio
import logging
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_tool_integration():
    """测试工具集成到系统中的运行"""
    print("🔧 Creatomate工具Agent系统集成测试")
    print("="*60)
    
    try:
        # 设置环境变量
        os.environ["CREATOMATE_API_KEY"] = "017ad773fca84ddea445ccbe72ea4d5d699a7e796b915e8a262db7c6e338bda44b83ebfaedba1186ac6196eebc74ab26"
        os.environ["CREATOMATE_API_URL"] = "https://api.creatomate.com/v1/renders"
        
        print("✅ 环境变量已设置")
        
        # 导入系统模块
        from src.config.configuration import Configuration
        from src.tools import creatomate_video_tool
        
        print("✅ 系统模块导入成功")
        
        # 验证配置
        config = Configuration.from_runnable_config()
        print(f"📋 Creatomate API Key: {config.creatomate_api_key[:10] if config.creatomate_api_key else '未配置'}...")
        print(f"📋 Creatomate API URL: {config.creatomate_api_url or '未配置'}")
        
        # 验证工具实例
        if creatomate_video_tool is None:
            print("❌ Creatomate工具未正确初始化")
            return False
        
        print(f"✅ 工具初始化成功: {creatomate_video_tool.name}")
        print(f"📋 工具描述: {creatomate_video_tool.description}")
        
        # 测试工具调用
        print("\n🧪 测试工具调用...")
        
        test_input = {
            "input_mode": "scenes",
            "scenes": [
                {
                    "duration": 2,
                    "background_video": "https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/30399726600-1-192.mp4",
                    "subtitle": "集成测试"
                }
            ],
            "video_width": 1280,
            "video_height": 720,
            "output_format": "mp4"
        }
        
        print("📝 测试输入:")
        print(json.dumps(test_input, ensure_ascii=False, indent=2))
        
        # 使用工具（同步调用）
        result = creatomate_video_tool.invoke(test_input)
        
        print("\n🎉 工具调用成功!")
        print("📋 返回结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2) if isinstance(result, dict) else str(result))
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        logger.error(f"详细错误: {str(e)}", exc_info=True)
        return False

def test_tool_registration():
    """测试工具注册情况"""
    print("\n🔍 工具注册情况检查")
    print("="*40)
    
    try:
        from src.tools import video_tools, ALL_TOOLS_LIST, TOOL_MAP
        
        print(f"📊 视频工具数量: {len(video_tools)}")
        
        for i, tool in enumerate(video_tools):
            if tool:
                print(f"  {i+1}. {tool.name}: {tool.description[:50]}...")
        
        # 检查Creatomate工具是否在工具映射中
        creatomate_tools = [name for name in TOOL_MAP.keys() if 'creatomate' in name.lower()]
        
        print(f"\n📋 Creatomate相关工具: {creatomate_tools}")
        
        if creatomate_tools:
            for tool_name in creatomate_tools:
                tool = TOOL_MAP[tool_name]
                print(f"✅ {tool_name}: 已注册")
                print(f"   描述: {tool.description}")
        else:
            print("⚠️ 未找到Creatomate相关工具")
        
        return len(creatomate_tools) > 0
        
    except Exception as e:
        print(f"❌ 工具注册检查失败: {str(e)}")
        return False

def test_video_agent_tools():
    """测试视频Agent工具列表"""
    print("\n🎬 视频Agent工具列表检查")
    print("="*40)
    
    try:
        from src.tools import video_tools
        
        print(f"📊 可用视频工具: {len(video_tools)}个")
        
        for i, tool in enumerate(video_tools):
            if tool:
                tool_name = tool.name
                is_creatomate = 'creatomate' in tool_name.lower()
                status = "🎯 新集成" if is_creatomate else "✅ 已有"
                print(f"  {i+1}. {status} {tool_name}")
                print(f"      {tool.description[:60]}...")
        
        # 统计Creatomate工具
        creatomate_count = sum(1 for tool in video_tools 
                             if tool and 'creatomate' in tool.name.lower())
        
        print(f"\n📋 Creatomate工具数量: {creatomate_count}个")
        
        return creatomate_count > 0
        
    except Exception as e:
        print(f"❌ 视频Agent工具检查失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 Creatomate工具Agent系统完整集成测试")
    print("="*60)
    
    results = []
    
    # 测试1: 工具注册
    print("📋 步骤1: 检查工具注册")
    registration_ok = test_tool_registration()
    results.append(("工具注册", registration_ok))
    
    # 测试2: 视频Agent工具列表
    print("\n📋 步骤2: 检查视频Agent工具")
    video_agent_ok = test_video_agent_tools()
    results.append(("视频Agent工具", video_agent_ok))
    
    # 测试3: 完整集成测试
    print("\n📋 步骤3: 完整工具集成测试")
    integration_ok = await test_tool_integration()
    results.append(("完整集成", integration_ok))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 集成测试结果汇总")
    print("="*60)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"• {test_name}: {status}")
        if success:
            success_count += 1
    
    total_tests = len(results)
    success_rate = success_count / total_tests * 100
    
    print(f"\n🎯 总体结果: {success_count}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 集成成功！Creatomate工具已成功集成到Agent系统")
        print("✅ 您现在可以在Agent工作流程中使用AI视频生成功能")
    else:
        print("⚠️ 集成未完全成功，请检查以上失败项目")
    
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
