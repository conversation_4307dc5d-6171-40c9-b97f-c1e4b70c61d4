#!/usr/bin/env python3
"""
通义听悟API调试版本
"""

import os
import json
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tongyi_with_debug():
    """调试版本的通义听悟测试"""
    logger.info("🎯 开始调试通义听悟API...")
    
    try:
        from aliyunsdkcore.client import AcsClient
        from aliyunsdkcore.request import CommonRequest
        from aliyunsdkcore.auth.credentials import AccessKeyCredential
        from aliyunsdkcore.acs_exception.exceptions import ServerException, ClientException
    except ImportError as e:
        logger.error(f"❌ 导入阿里云SDK失败: {e}")
        return False
    
    # 配置信息
    access_key_id = "LTAI5tBTpMAPNJ3Z6jpZyTSo"
    access_key_secret = "******************************"
    app_key = "TQvDYpoD8ofQPIeG"
    
    logger.info(f"🔑 Access Key ID: {access_key_id}")
    logger.info(f"🆔 App Key: {app_key}")
    
    try:
        # 创建客户端
        credentials = AccessKeyCredential(access_key_id, access_key_secret)
        client = AcsClient(region_id='cn-beijing', credential=credentials)
        logger.info("✅ 客户端创建成功")
        
        # 构建请求体
        request_body = {
            "AppKey": app_key,
            "Input": {
                "SourceLanguage": "cn",
                "TaskKey": f"test_debug_{int(time.time())}",
                "FileUrl": "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/bilibili_BV1Jq4y1C7F8_GAOQING.mp4"
            },
            "Parameters": {
                "Transcription": {
                    "DiarizationEnabled": True,
                    "Diarization": {
                        "SpeakerCount": 0
                    }
                }
            }
        }
        
        logger.info("📋 请求体:")
        logger.info(json.dumps(request_body, indent=2, ensure_ascii=False))
        
        # 创建请求
        request = CommonRequest()
        request.set_accept_format('json')
        request.set_domain('tingwu.cn-beijing.aliyuncs.com')
        request.set_version('2023-09-30')
        request.set_protocol_type('https')
        request.set_method('PUT')
        request.set_uri_pattern('/openapi/tingwu/v2/tasks')
        request.add_header('Content-Type', 'application/json')
        request.add_query_param('type', 'offline')
        request.set_content(json.dumps(request_body).encode('utf-8'))
        
        logger.info("📤 发送请求...")
        
        # 发送请求
        response = client.do_action_with_exception(request)
        response_data = json.loads(response)
        
        logger.info("✅ 请求成功")
        logger.info(f"📥 响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        
        return True
        
    except ServerException as e:
        logger.error(f"❌ 服务器错误: {e}")
        logger.error(f"   错误码: {e.get_error_code()}")
        logger.error(f"   错误信息: {e.get_error_msg()}")
        logger.error(f"   请求ID: {e.get_request_id()}")
        logger.error(f"   HTTP状态: {e.get_http_status()}")
        
        # 分析常见错误
        error_code = e.get_error_code()
        if "InvalidTenant" in error_code:
            logger.info("\n💡 可能的解决方案:")
            logger.info("1. 检查AppKey是否正确")
            logger.info("2. 确认通义听悟服务已开通")
            logger.info("3. 检查项目状态是否正常")
            logger.info("4. 确认AccessKey有通义听悟的权限")
        
        return False
        
    except ClientException as e:
        logger.error(f"❌ 客户端错误: {e}")
        logger.error(f"   错误码: {e.get_error_code()}")
        logger.error(f"   错误信息: {e.get_error_msg()}")
        return False
        
    except Exception as e:
        logger.error(f"❌ 未知错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_service_status():
    """检查服务状态"""
    logger.info("🔍 检查通义听悟服务状态...")
    
    logger.info("📋 检查清单:")
    logger.info("1. ✅ 阿里云账户已创建")
    logger.info("2. ✅ AccessKey已配置")
    logger.info("3. ✅ 通义听悟AppKey已获取")
    logger.info("4. ❓ 通义听悟服务是否已开通?")
    logger.info("5. ❓ 项目状态是否正常?")
    logger.info("6. ❓ AccessKey是否有足够权限?")
    
    logger.info("\n🔗 相关链接:")
    logger.info("• 通义听悟控制台: https://nls-portal.console.aliyun.com/tingwu/projects")
    logger.info("• RAM权限管理: https://ram.console.aliyun.com/")
    logger.info("• 账户余额查询: https://usercenter2.aliyun.com/finance/overview")

def test_simple_request():
    """测试最简单的请求"""
    logger.info("🧪 测试最简单的请求...")
    
    try:
        from aliyunsdkcore.client import AcsClient
        from aliyunsdkcore.request import CommonRequest
        from aliyunsdkcore.auth.credentials import AccessKeyCredential
        from aliyunsdkcore.acs_exception.exceptions import ServerException, ClientException
        
        # 配置
        access_key_id = "LTAI5tBTpMAPNJ3Z6jpZyTSo"
        access_key_secret = "******************************"
        app_key = "TQvDYpoD8ofQPIeG"
        
        # 创建客户端
        credentials = AccessKeyCredential(access_key_id, access_key_secret)
        client = AcsClient(region_id='cn-beijing', credential=credentials)
        
        # 最简单的请求体
        simple_request_body = {
            "AppKey": app_key,
            "Input": {
                "SourceLanguage": "cn",
                "TaskKey": f"simple_test_{int(time.time())}",
                "FileUrl": "https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/asr_example.wav"  # 使用官方示例音频
            }
        }
        
        logger.info("📋 简化请求体:")
        logger.info(json.dumps(simple_request_body, indent=2, ensure_ascii=False))
        
        # 创建请求
        request = CommonRequest()
        request.set_accept_format('json')
        request.set_domain('tingwu.cn-beijing.aliyuncs.com')
        request.set_version('2023-09-30')
        request.set_protocol_type('https')
        request.set_method('PUT')
        request.set_uri_pattern('/openapi/tingwu/v2/tasks')
        request.add_header('Content-Type', 'application/json')
        request.add_query_param('type', 'offline')
        request.set_content(json.dumps(simple_request_body).encode('utf-8'))
        
        logger.info("📤 发送简化请求...")
        
        # 发送请求
        response = client.do_action_with_exception(request)
        response_data = json.loads(response)
        
        logger.info("✅ 简化请求成功!")
        logger.info(f"📥 响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 简化请求也失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 通义听悟API调试测试")
    logger.info("="*60)
    
    # 检查服务状态
    check_service_status()
    
    logger.info("\n" + "="*60)
    logger.info("🧪 测试1: 简化请求")
    logger.info("="*60)
    
    # 先测试简化请求
    simple_success = test_simple_request()
    
    if simple_success:
        logger.info("\n" + "="*60)
        logger.info("🧪 测试2: 完整请求（包含说话人分离）")
        logger.info("="*60)
        
        # 如果简化请求成功，再测试完整请求
        full_success = test_tongyi_with_debug()
        
        if full_success:
            logger.info("\n🎉 通义听悟API测试成功!")
        else:
            logger.info("\n⚠️ 简化请求成功，但完整请求失败")
    else:
        logger.info("\n❌ 基础API调用失败，需要检查配置")
    
    logger.info("\n" + "="*60)
    logger.info("🎯 调试总结")
    logger.info("="*60)
    
    if simple_success:
        logger.info("✅ 通义听悟API基础功能正常")
        logger.info("✅ AccessKey和AppKey配置正确")
        logger.info("📝 可以继续测试说话人分离功能")
    else:
        logger.info("❌ 通义听悟API基础配置有问题")
        logger.info("🔧 需要检查账户、权限、服务开通状态")

if __name__ == "__main__":
    main()
