#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频分割器 - 根据说话人分别拼接音频
为每个说话人生成约1分钟的音频片段
"""

import json
import os
import sys
from pathlib import Path
from pydub import AudioSegment
from typing import Dict, List, Tuple
import argparse


class AudioSplitter:
    def __init__(self, json_file: str, output_dir: str = "output_audio"):
        """
        初始化音频分割器
        
        Args:
            json_file: 包含音频分析结果的JSON文件路径
            output_dir: 输出目录
        """
        self.json_file = json_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 加载JSON数据
        with open(json_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
    
    def get_speaker_segments(self) -> Dict[str, List[Dict]]:
        """
        按说话人ID分组音频片段
        
        Returns:
            字典，键为说话人ID，值为该说话人的所有音频片段列表
        """
        speaker_segments = {}
        
        for subtitle in self.data['subtitles']:
            speaker_id = subtitle['speaker_id']
            if speaker_id not in speaker_segments:
                speaker_segments[speaker_id] = []
            speaker_segments[speaker_id].append(subtitle)
        
        return speaker_segments
    
    def load_audio_segment(self, file_path: str) -> AudioSegment:
        """
        加载音频片段
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            AudioSegment对象
        """
        if not os.path.exists(file_path):
            print(f"警告: 音频文件不存在: {file_path}")
            return None
        
        try:
            return AudioSegment.from_wav(file_path)
        except Exception as e:
            print(f"错误: 无法加载音频文件 {file_path}: {e}")
            return None
    
    def create_speaker_audio(self, speaker_id: str, segments: List[Dict], 
                           target_duration: int = 60000) -> AudioSegment:
        """
        为指定说话人创建拼接音频
        
        Args:
            speaker_id: 说话人ID
            segments: 该说话人的音频片段列表
            target_duration: 目标时长（毫秒），默认60秒
            
        Returns:
            拼接后的AudioSegment对象
        """
        combined_audio = AudioSegment.empty()
        current_duration = 0
        
        print(f"正在处理说话人 {speaker_id}...")
        
        # 按时间顺序排序
        segments.sort(key=lambda x: x['start_time'])
        
        for i, segment in enumerate(segments):
            if current_duration >= target_duration:
                break
                
            # 从audio_segments中获取文件路径
            audio_segments = self.data.get('audio_segments', {})
            speaker_name = segment.get('speaker_name', '主持人')
            
            # 查找对应的音频文件
            audio_file = None
            if speaker_name in audio_segments:
                for seg in audio_segments[speaker_name]['segments']:
                    if (abs(seg['start_time'] - segment['start_time']/1000) < 0.1 and 
                        abs(seg['end_time'] - segment['end_time']/1000) < 0.1):
                        audio_file = seg['file_path']
                        break
            
            if not audio_file:
                print(f"警告: 找不到片段 {i+1} 的音频文件")
                continue
            
            # 加载音频片段
            audio_segment = self.load_audio_segment(audio_file)
            if audio_segment is None:
                continue
            
            # 检查是否会超过目标时长
            remaining_time = target_duration - current_duration
            if len(audio_segment) > remaining_time:
                # 截取部分音频以达到目标时长
                audio_segment = audio_segment[:remaining_time]
            
            # 添加到合并音频中
            combined_audio += audio_segment
            current_duration += len(audio_segment)
            
            print(f"  添加片段 {i+1}: {segment['text'][:30]}... "
                  f"({len(audio_segment)/1000:.1f}秒)")
        
        print(f"说话人 {speaker_id} 总时长: {len(combined_audio)/1000:.1f}秒")
        return combined_audio
    
    def split_and_save(self, target_duration: int = 60):
        """
        分割并保存每个说话人的音频
        
        Args:
            target_duration: 目标时长（秒）
        """
        target_duration_ms = target_duration * 1000
        speaker_segments = self.get_speaker_segments()
        
        print(f"发现 {len(speaker_segments)} 个说话人")
        print(f"目标时长: {target_duration} 秒")
        print("-" * 50)
        
        for speaker_id, segments in speaker_segments.items():
            speaker_name = segments[0]['speaker_name'] if segments else f"说话人{speaker_id}"
            
            print(f"\n处理说话人 {speaker_id} ({speaker_name})")
            print(f"总片段数: {len(segments)}")
            
            # 创建拼接音频
            combined_audio = self.create_speaker_audio(
                speaker_id, segments, target_duration_ms
            )
            
            if len(combined_audio) > 0:
                # 保存音频文件
                output_file = self.output_dir / f"说话人{speaker_id}_{speaker_name}_{len(combined_audio)//1000}秒.wav"
                combined_audio.export(str(output_file), format="wav")
                print(f"已保存: {output_file}")
            else:
                print(f"警告: 说话人 {speaker_id} 没有有效的音频数据")
        
        print(f"\n所有音频文件已保存到: {self.output_dir}")


def main():
    parser = argparse.ArgumentParser(description="根据说话人分割音频")
    parser.add_argument("json_file", help="音频分析结果JSON文件路径")
    parser.add_argument("-o", "--output", default="output_audio", 
                       help="输出目录 (默认: output_audio)")
    parser.add_argument("-d", "--duration", type=int, default=60,
                       help="每个说话人的目标时长（秒，默认: 60）")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.json_file):
        print(f"错误: 文件不存在: {args.json_file}")
        sys.exit(1)
    
    try:
        splitter = AudioSplitter(args.json_file, args.output)
        splitter.split_and_save(args.duration)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
